# 红颜一梦 加密解密工具 v1.2

## 简介

这是一个专门为《红颜一梦》游戏制作的文件加密解密工具，基于游戏源码中的SaveManager类实现。支持对游戏中的lua文件和xml文件进行加密和解密操作。

## 功能特点

- ✅ **批量处理**：支持批量处理整个文件夹中的文件
- ✅ **单文件处理**：支持单独处理lua和xml文件
- ✅ **快捷操作**：支持Ctrl+V粘贴文件路径
- ✅ **智能切换**：根据选择内容自动切换处理模式
- ✅ **直接覆盖**：处理后直接覆盖原文件，无需手动替换
- ✅ **自动识别**：自动识别文件的加密格式
- ✅ **CRC校验**：支持CRC校验确保数据完整性
- ✅ **图形界面**：简洁易用的图形化操作界面
- ✅ **多种格式**：支持lua文件(.lua)和xml文件(.xml)
- ✅ **安全可靠**：基于游戏原版加密算法，确保兼容性

## 支持的加密格式

### Lua文件加密格式
1. **CRC加密格式**：`数字@加密内容` (如: `20323@nycNJ7TLSw/8sr...`)
2. **ExtractString格式**：`@开头的特殊编码` (如: `@QWERTYuiop...`)

### XML文件加密格式
1. **CRC加密格式**：`数字@加密内容` (如: `110108@o2V8tGmzXR...`)

## 使用方法

### 1. 启动程序
双击 `红颜一梦加密解密工具_v1.2.exe` 启动程序

### 2. 选择处理模式
- **批量处理文件夹**：处理整个文件夹中的所有符合条件的文件
- **单独处理文件**：只处理选中的单个文件

### 3. 选择文件/文件夹
- **方法一**：点击"浏览"按钮选择（推荐）
- **方法二**：复制路径后按Ctrl+V粘贴

### 4. 选择操作类型
- **解密文件**：将加密的文件解密为可读的明文
- **加密文件**：将明文文件加密为游戏可识别的格式

### 5. 选择文件类型（仅批量处理模式）
- **Lua文件(.lua)**：只处理.lua扩展名的文件
- **XML文件(.xml)**：只处理.xml扩展名的文件
- **全部文件**：同时处理.lua和.xml文件
- **注意**：单文件模式下会自动识别文件类型

### 6. 开始处理
点击"开始处理"按钮，程序会自动处理选中的文件或文件夹

### 7. 查看结果
- 处理过程会在日志区域显示详细信息
- **重要**：处理后会直接覆盖原文件，无需手动替换
- 建议在处理前备份重要文件

## 示例

### 解密示例
```
原文件：AI.lua (加密内容)
处理后：AI.lua (直接覆盖为可读的lua代码)
```

### 加密示例
```
原文件：touch.xml (明文xml)
处理后：touch.xml (直接覆盖为加密内容)
```

## 注意事项

1. **备份重要文件**：处理前请备份重要文件，避免数据丢失
2. **文件编码**：程序使用UTF-8编码处理文件，确保中文字符正常显示
3. **文件格式**：只有符合游戏加密格式的文件才能被正确解密
4. **权限问题**：确保程序有读写目标文件夹的权限

## 技术说明

### 加密算法
- **3DES加密**：使用TripleDES算法进行数据加密
- **Base64编码**：对加密数据进行Base64编码
- **CRC16校验**：使用CRC16算法进行数据完整性校验
- **字符映射**：使用特定的字符映射表进行字符转换

### 密钥信息
- 加密密钥：基于字符串 "Yh$45Ct@mods" 的MD5哈希值
- 算法模式：ECB模式，PKCS7填充

## 常见问题

### Q: 为什么有些文件解密失败？
A: 可能的原因：
- 文件不是游戏的加密格式
- 文件已经是明文格式
- 文件损坏或格式不正确

### Q: 解密后的文件乱码怎么办？
A: 请确保：
- 使用UTF-8编码打开文件
- 文件确实是游戏的加密格式
- 原文件没有损坏

### Q: 可以处理其他游戏的文件吗？
A: 本工具专门为《红颜一梦》游戏设计，不保证对其他游戏文件的兼容性

## 版本信息

- **版本**：v1.2
- **开发语言**：Python 3.11
- **界面框架**：Tkinter + tkinterdnd2
- **加密库**：PyCryptodome
- **打包工具**：PyInstaller

## 版本更新

### v1.2 (2024-08-19)
- ✅ 新增单独处理文件功能
- ✅ 智能模式切换（批量/单文件）
- ✅ 优化界面布局和用户体验
- ✅ 增强错误处理和兼容性
- ✅ 提高程序稳定性

### v1.1 (2024-08-19)
- ✅ 新增Ctrl+V粘贴路径功能
- ✅ 改为直接覆盖原文件
- ✅ 优化界面布局和提示信息
- ✅ 增强用户体验

### v1.0 (2024-08-19)
- ✅ 首次发布基础功能

## 免责声明

本工具仅供学习和研究使用，请勿用于商业用途。使用本工具产生的任何问题，开发者不承担责任。请在使用前备份重要文件。

---

**开发者**：基于《红颜一梦》游戏源码分析制作  
**版本日期**：2024年  
**联系方式**：如有问题请通过相关渠道反馈
