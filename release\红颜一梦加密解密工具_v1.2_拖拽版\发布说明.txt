红颜一梦 加密解密工具 v1.2 拖拽版发布包
=========================================

文件清单：
---------
📁 dist/
  └── 红颜一梦加密解密工具_v1.2.exe  (主程序，约20MB)
📄 使用说明.md                     (详细使用说明)
📄 运行工具.bat                    (快速启动脚本)
📄 发布说明.txt                    (本文件)
📄 问题解决方案.md                 (故障排除指南)

快速开始：
---------
1. 双击 "红颜一梦加密解密工具_v1.2.exe" 启动程序
   或者双击 "运行工具.bat" 启动

2. 选择处理模式（批量处理文件夹/单独处理文件）

3. 拖拽文件/文件夹到程序窗口或点击浏览选择

4. 选择加密或解密操作

5. 点击"开始处理"

⚠️ 重要：处理后会直接覆盖原文件，请提前备份！

主要功能：
---------
✅ 解密游戏中的加密lua文件
✅ 解密游戏中的加密xml文件
✅ 将明文文件加密为游戏格式
✅ 批量处理整个文件夹
✅ 单独处理lua和xml文件
✅ 拖拽文件/文件夹到程序窗口
✅ 快捷键粘贴路径（Ctrl+V）
✅ 智能模式切换
✅ 直接覆盖原文件
✅ 图形化操作界面
✅ 详细的处理日志

支持格式：
---------
• Lua文件 (.lua)
  - CRC加密格式: 数字@内容
  - ExtractString格式: @开头编码

• XML文件 (.xml)
  - CRC加密格式: 数字@内容

系统要求：
---------
• Windows 7/8/10/11 (64位)
• 无需安装Python环境
• 无需额外依赖库

注意事项：
---------
⚠️ 处理前请备份重要文件
⚠️ 确保有文件夹读写权限
⚠️ 杀毒软件可能误报，请添加信任

技术信息：
---------
• 基于游戏源码SaveManager类实现
• 使用3DES+Base64+CRC16算法
• Python 3.11 + PyInstaller打包
• 完全兼容游戏原版加密格式

版本历史：
---------
v1.2 拖拽版 (2024-08-19)
- 新增真正的拖拽功能支持（Windows原生API）
- 新增单独处理文件功能
- 智能模式切换（批量/单文件）
- 自动识别拖拽内容类型
- 优化界面布局和用户体验
- 增强错误处理和兼容性
- 提高程序稳定性

v1.1 (2024-08-19)
- 新增Ctrl+V粘贴路径功能
- 改为直接覆盖原文件
- 优化界面布局和提示
- 增强用户体验

v1.0 (2024-08-19)
- 首次发布
- 支持lua和xml文件加密解密
- 图形化界面
- 批量处理功能

联系反馈：
---------
如有问题或建议，请通过相关渠道反馈。

免责声明：
---------
本工具仅供学习研究使用，请勿用于商业用途。
使用本工具产生的任何问题，开发者不承担责任。
请在使用前备份重要文件。

=====================================
开发者：基于红颜一梦游戏源码分析制作
版本：v1.2
日期：2024年8月19日
