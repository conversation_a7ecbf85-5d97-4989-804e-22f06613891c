# 红颜一梦加密解密工具 v1.2 最终版本说明

## 🎉 版本完成

经过多次迭代和优化，红颜一梦加密解密工具v1.2最终版本已经完成！这个版本专注于稳定性和实用性，提供了强大的单文件处理功能。

## ✨ 主要功能

### 1. 双模式处理
- **批量处理文件夹**：一次性处理整个文件夹中的所有lua/xml文件
- **单独处理文件**：精确处理单个lua或xml文件

### 2. 智能操作
- **自动模式切换**：根据选择的内容自动切换处理模式
- **文件类型识别**：单文件模式下自动识别文件类型
- **直接覆盖**：处理后直接覆盖原文件，无需手动替换

### 3. 便捷操作
- **浏览选择**：点击浏览按钮选择文件或文件夹
- **快捷粘贴**：支持Ctrl+V粘贴文件路径
- **实时反馈**：详细的处理日志和状态提示

## 🔧 技术特点

### 加密算法
- **3DES加密**：使用TripleDES算法确保安全性
- **Base64编码**：对加密数据进行Base64编码
- **CRC16校验**：确保数据完整性和正确性
- **字符映射**：使用特定的字符转换表

### 支持格式
- **Lua文件**：支持CRC格式和ExtractString格式
- **XML文件**：支持CRC加密格式
- **自动识别**：智能识别文件的加密状态

### 界面设计
- **现代化界面**：使用Tkinter构建的清洁界面
- **响应式布局**：支持窗口大小调整
- **中文支持**：完整的中文界面和文档

## 📦 发布包内容

```
📁 红颜一梦加密解密工具_v1.2_final/
├── 📄 红颜一梦加密解密工具_v1.2.exe  (19.7MB)
├── 📄 使用说明.md                   (详细使用指南)
├── 📄 发布说明.txt                  (快速入门)
├── 📄 运行工具.bat                  (一键启动)
├── 📄 问题解决方案.md               (故障排除)
└── 📁 示例文件/
    ├── 📄 AI_encrypted.lua          (加密的lua示例)
    ├── 📄 touch_encrypted.xml       (加密的xml示例)
    └── 📄 示例说明.txt              (示例文件说明)
```

## 🚀 使用场景

### 游戏MOD开发
1. 解密游戏lua文件查看逻辑
2. 修改游戏参数和功能
3. 重新加密文件应用到游戏

### 配置文件修改
1. 解密游戏配置xml文件
2. 调整游戏设置和参数
3. 加密后替换原文件

### 批量文件处理
1. 解密整个文件夹的游戏资源
2. 批量修改多个文件
3. 重新加密所有修改的文件

## ⚠️ 重要提醒

### 使用前必读
1. **备份文件**：处理前务必备份重要的游戏文件
2. **测试验证**：建议先用示例文件测试功能
3. **权限确认**：确保程序有文件读写权限
4. **杀毒软件**：可能需要添加程序到信任列表

### 操作注意事项
1. **直接覆盖**：程序会直接覆盖原文件，无法撤销
2. **文件格式**：只处理符合游戏加密格式的文件
3. **编码支持**：使用UTF-8编码，支持中文字符
4. **错误处理**：遇到问题时查看日志信息

## 🔍 故障排除

### 程序无法启动
- 确保Windows系统版本兼容
- 检查是否被杀毒软件拦截
- 尝试以管理员身份运行

### 处理失败
- 检查文件是否为游戏原版格式
- 确认文件没有损坏
- 验证程序有足够的权限

### 中文乱码
- 确保系统支持UTF-8编码
- 使用支持UTF-8的文本编辑器
- 检查原文件的编码格式

## 📈 版本历史

### v1.2 (最终版本)
- ✅ 新增单独处理文件功能
- ✅ 智能模式切换（批量/单文件）
- ✅ 优化界面布局和用户体验
- ✅ 增强错误处理和兼容性
- ✅ 提高程序稳定性
- ✅ 完善文档和说明

### v1.1
- ✅ 新增Ctrl+V粘贴路径功能
- ✅ 改为直接覆盖原文件
- ✅ 优化界面布局和提示信息

### v1.0
- ✅ 首次发布基础功能
- ✅ 支持lua和xml文件加密解密
- ✅ 图形化界面
- ✅ 批量处理功能

## 🎯 设计理念

### 简单易用
- 直观的操作界面
- 清晰的功能分类
- 详细的操作提示

### 功能完整
- 支持所有游戏文件格式
- 提供批量和单文件两种模式
- 完整的加密解密功能

### 稳定可靠
- 基于游戏原版算法
- 充分的测试验证
- 完善的错误处理

## 📞 技术支持

### 获取帮助
1. 查看使用说明.md文档
2. 阅读问题解决方案.md
3. 使用示例文件测试功能
4. 通过相关渠道反馈问题

### 反馈信息
反馈问题时请提供：
- 操作系统版本
- 错误信息截图
- 操作步骤描述
- 问题文件（如可能）

## 🏆 总结

红颜一梦加密解密工具v1.2最终版本是一个功能完整、稳定可靠的游戏文件处理工具。它不仅保持了原有的批量处理能力，还新增了精确的单文件处理功能，为游戏MOD开发者和玩家提供了强大的工具支持。

通过智能的模式切换、直观的操作界面和完善的错误处理，这个工具能够满足各种游戏文件处理需求，是《红颜一梦》游戏爱好者的必备工具。

---
**开发者**：基于红颜一梦游戏源码分析制作  
**版本**：v1.2 最终版本  
**发布日期**：2024年8月19日  
**文件大小**：19.7MB  
**系统要求**：Windows 7/8/10/11 (64位)
