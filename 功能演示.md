# 红颜一梦加密解密工具 v1.1 功能演示

## 🎯 新功能亮点

### 1. 拖拽文件夹功能 🖱️
- **操作方式**：直接从文件管理器拖拽文件夹到程序输入框
- **支持类型**：文件夹和文件都可以拖拽
- **智能识别**：拖拽文件时自动使用其父目录
- **即时反馈**：拖拽后立即显示路径和日志信息

### 2. 直接覆盖原文件 💾
- **处理方式**：加密/解密后直接覆盖原文件
- **无需手动**：不再生成额外的_encrypted或_decrypted文件
- **即时生效**：处理完成后文件立即可用
- **安全提醒**：界面明确提示用户提前备份

## 🚀 使用流程演示

### 场景一：解密游戏文件
```
1. 启动程序 → 红颜一梦加密解密工具_v1.1.exe
2. 拖拽文件夹 → 将包含加密lua/xml文件的文件夹拖到输入框
3. 选择操作 → 点击"解密文件"
4. 选择类型 → 选择"全部文件"或特定类型
5. 开始处理 → 点击"开始处理"按钮
6. 查看结果 → 原文件已被解密内容覆盖
```

### 场景二：加密修改后的文件
```
1. 启动程序 → 红颜一梦加密解密工具_v1.1.exe
2. 拖拽文件夹 → 将包含修改后明文文件的文件夹拖到输入框
3. 选择操作 → 点击"加密文件"
4. 选择类型 → 选择"全部文件"或特定类型
5. 开始处理 → 点击"开始处理"按钮
6. 查看结果 → 原文件已被加密内容覆盖
```

## 📋 界面改进

### 新增提示信息
- 💡 拖拽提示：`可以直接拖拽文件夹到上方输入框`
- ⚠️ 覆盖警告：`注意：处理后将直接覆盖原文件，请提前备份！`
- 📊 处理状态：使用表情符号增强日志可读性

### 优化的日志显示
```
✅ 解密成功: AI.lua (已覆盖原文件)
✅ 加密成功: touch.xml (已覆盖原文件)
⚠️ 跳过: test.lua (文件可能已经是明文)
❌ 解密失败: broken.lua (格式不正确或已损坏)
```

## 🔧 技术改进

### 拖拽支持
- **依赖库**：tkinterdnd2
- **事件处理**：`<<Drop>>` 事件绑定
- **路径解析**：自动处理文件和文件夹路径
- **错误处理**：拖拽异常的容错机制

### 文件覆盖
- **安全写入**：使用UTF-8编码确保中文兼容
- **原子操作**：直接写入原文件路径
- **状态反馈**：详细的成功/失败信息
- **批量处理**：支持整个文件夹的批量覆盖

## 📦 发布包内容

```
📁 红颜一梦加密解密工具_v1.1/
├── 📄 红颜一梦加密解密工具_v1.1.exe  (主程序 19.7MB)
├── 📄 使用说明.md                   (详细说明文档)
├── 📄 发布说明.txt                  (快速入门指南)
├── 📄 运行工具.bat                  (一键启动脚本)
└── 📁 示例文件/
    ├── 📄 AI_encrypted.lua          (加密的lua示例)
    ├── 📄 touch_encrypted.xml       (加密的xml示例)
    └── 📄 示例说明.txt              (示例文件说明)
```

## ⚠️ 重要提醒

### 使用前必读
1. **备份文件**：处理前务必备份重要文件
2. **测试验证**：建议先用示例文件测试功能
3. **权限确认**：确保程序有文件夹读写权限
4. **杀毒软件**：可能需要添加程序到信任列表

### 适用场景
- ✅ 游戏MOD开发和调试
- ✅ 游戏文件内容查看和修改
- ✅ 批量处理游戏资源文件
- ✅ 学习游戏文件格式和加密机制

## 🎉 版本对比

| 功能 | v1.0 | v1.1 |
|------|------|------|
| 基础加密解密 | ✅ | ✅ |
| 批量处理 | ✅ | ✅ |
| 图形界面 | ✅ | ✅ |
| 拖拽支持 | ❌ | ✅ |
| 直接覆盖 | ❌ | ✅ |
| 生成副本文件 | ✅ | ❌ |
| 界面优化 | 基础 | 增强 |

## 📞 反馈与支持

如果在使用过程中遇到问题或有改进建议，请通过相关渠道反馈。

---
**开发者**：基于红颜一梦游戏源码分析制作  
**版本**：v1.1  
**更新日期**：2024年8月19日
