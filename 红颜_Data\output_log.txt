Initialize engine version: 5.2.4f1 (98095704e6fe)
GfxDevice: creating device client; threaded=1
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: Intel(R) Iris(R) Xe Graphics (ID=0x46a6)
    Vendor:   Intel
    VRAM:     128 MB
Begin MonoManager ReloadAssembly
Platform assembly: D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\UnityEngine.dll (this message is harmless)
Loading D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\UnityEngine.dll into Unity Child Domain
Platform assembly: D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\Assembly-CSharp-firstpass.dll (this message is harmless)
Loading D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\Assembly-CSharp-firstpass.dll into Unity Child Domain
Platform assembly: D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\Assembly-CSharp.dll (this message is harmless)
Loading D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\Assembly-CSharp.dll into Unity Child Domain
Platform assembly: D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\UnityEngine.UI.dll (this message is harmless)
Loading D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\UnityEngine.UI.dll into Unity Child Domain
Platform assembly: D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\UnityEngine.Networking.dll (this message is harmless)
Loading D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\UnityEngine.Networking.dll into Unity Child Domain
Platform assembly: D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\DOTween.dll (this message is harmless)
Loading D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\DOTween.dll into Unity Child Domain
Platform assembly: D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\DOTween43.dll (this message is harmless)
Loading D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\DOTween43.dll into Unity Child Domain
Platform assembly: D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\DOTween46.dll (this message is harmless)
Loading D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\DOTween46.dll into Unity Child Domain
Platform assembly: D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\Debuger.dll (this message is harmless)
Loading D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\Debuger.dll into Unity Child Domain
Platform assembly: D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\System.Core.dll (this message is harmless)
- Completed reload, in  0.038 seconds
<RI> Initializing input.

<RI> Input initialized.

desktop: 1920x1080 60Hz; virtual: 1920x1080 at 0,0
<RI> Initialized touch support.

Platform assembly: D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\System.dll (this message is harmless)
Platform assembly: D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\System.Xml.dll (this message is harmless)
Platform assembly: D:\Thunderbolt cloud disk\HYYM\红颜_Data\Managed\System.Configuration.dll (this message is harmless)
Unloading 4 Unused Serialized files (Serialized files now loaded: 0)
Setting up 4 worker threads for Enlighten.
  Thread -> id: 4e08 -> priority: 1 
  Thread -> id: 277c -> priority: 1 
  Thread -> id: 3c6c -> priority: 1 
  Thread -> id: 6184 -> priority: 1 
UnloadTime: 1.852700 ms

Unloading 25 unused Assets to reduce memory usage. Loaded Objects now: 506.
Total: 0.449800 ms (FindLiveObjects: 0.022000 ms CreateObjectMapping: 0.012500 ms MarkObjects: 0.403800 ms  DeleteObjects: 0.010900 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 0)

Unloading 32 unused Assets to reduce memory usage. Loaded Objects now: 533.
Total: 0.761100 ms (FindLiveObjects: 0.019100 ms CreateObjectMapping: 0.014200 ms MarkObjects: 0.706500 ms  DeleteObjects: 0.021100 ms)




System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

重复key:曼陀支线姬雪2,xml=storysPY.xml
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

The referenced script on this Behaviour is missing!
 
(Filename:  Line: 1649)

Unloading 2 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 0.584700 ms

Unloading 52 unused Assets to reduce memory usage. Loaded Objects now: 887.
Total: 39.091198 ms (FindLiveObjects: 0.030200 ms CreateObjectMapping: 0.021000 ms MarkObjects: 38.997501 ms  DeleteObjects: 0.041300 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 5)

Unloading 2 unused Assets to reduce memory usage. Loaded Objects now: 885.
Total: 41.886898 ms (FindLiveObjects: 0.040600 ms CreateObjectMapping: 0.016200 ms MarkObjects: 41.812000 ms  DeleteObjects: 0.017500 ms)

读取存档失败: XML解析错误!
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

读取存档失败: XML解析错误!
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

读取存档失败: XML解析错误!
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

读取存档失败: XML解析错误!
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

读取存档失败: XML解析错误!
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

Unloading 2 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 1.491500 ms

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 552.
Total: 37.575497 ms (FindLiveObjects: 0.027900 ms CreateObjectMapping: 0.011900 ms MarkObjects: 37.504902 ms  DeleteObjects: 0.030200 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 12 unused Assets to reduce memory usage. Loaded Objects now: 554.
Total: 38.250599 ms (FindLiveObjects: 0.018400 ms CreateObjectMapping: 0.014100 ms MarkObjects: 38.197399 ms  DeleteObjects: 0.020100 ms)

Unloading 4 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 0.448400 ms

Unloading 6 unused Assets to reduce memory usage. Loaded Objects now: 1841.
Total: 37.496201 ms (FindLiveObjects: 0.045300 ms CreateObjectMapping: 0.029300 ms MarkObjects: 37.282898 ms  DeleteObjects: 0.138100 ms)

Unloading 2 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 1.598900 ms

Unloading 105 unused Assets to reduce memory usage. Loaded Objects now: 612.
Total: 36.975903 ms (FindLiveObjects: 0.034200 ms CreateObjectMapping: 0.015800 ms MarkObjects: 36.875401 ms  DeleteObjects: 0.050000 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 12 unused Assets to reduce memory usage. Loaded Objects now: 614.
Total: 39.613800 ms (FindLiveObjects: 0.022400 ms CreateObjectMapping: 0.014800 ms MarkObjects: 39.554996 ms  DeleteObjects: 0.021000 ms)

The file 'D:/Thunderbolt cloud disk/HYYM/红颜_Data/level2' is corrupted! Remove it and launch unity again!
[Position out of bounds!]
 
(Filename:  Line: 241)

Unloading 4 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 0.462200 ms

Unloading 28 unused Assets to reduce memory usage. Loaded Objects now: 3591.
Total: 42.779099 ms (FindLiveObjects: 0.111300 ms CreateObjectMapping: 0.044200 ms MarkObjects: 42.466801 ms  DeleteObjects: 0.156200 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3591.
Total: 40.219097 ms (FindLiveObjects: 0.092300 ms CreateObjectMapping: 0.039200 ms MarkObjects: 40.082600 ms  DeleteObjects: 0.004500 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3591.
Total: 41.175400 ms (FindLiveObjects: 0.119600 ms CreateObjectMapping: 0.038100 ms MarkObjects: 41.012398 ms  DeleteObjects: 0.004500 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3591.
Total: 40.884201 ms (FindLiveObjects: 0.103400 ms CreateObjectMapping: 0.038200 ms MarkObjects: 40.738098 ms  DeleteObjects: 0.003900 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3591.
Total: 40.688999 ms (FindLiveObjects: 0.129400 ms CreateObjectMapping: 0.049600 ms MarkObjects: 40.505199 ms  DeleteObjects: 0.004000 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3591.
Total: 41.262199 ms (FindLiveObjects: 0.101400 ms CreateObjectMapping: 0.041600 ms MarkObjects: 41.114403 ms  DeleteObjects: 0.004400 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3591.
Total: 40.209301 ms (FindLiveObjects: 0.100300 ms CreateObjectMapping: 0.066200 ms MarkObjects: 40.037201 ms  DeleteObjects: 0.005100 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3591.
Total: 40.571400 ms (FindLiveObjects: 0.100600 ms CreateObjectMapping: 0.047100 ms MarkObjects: 40.418201 ms  DeleteObjects: 0.004600 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3591.
Total: 39.656403 ms (FindLiveObjects: 0.097500 ms CreateObjectMapping: 0.040800 ms MarkObjects: 39.513100 ms  DeleteObjects: 0.004300 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3591.
Total: 41.072800 ms (FindLiveObjects: 0.126500 ms CreateObjectMapping: 0.047900 ms MarkObjects: 40.893600 ms  DeleteObjects: 0.004100 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 13 unused Assets to reduce memory usage. Loaded Objects now: 3554.
Total: 42.793800 ms (FindLiveObjects: 0.179600 ms CreateObjectMapping: 0.064000 ms MarkObjects: 42.377598 ms  DeleteObjects: 0.172000 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 2 unused Assets to reduce memory usage. Loaded Objects now: 3597.
Total: 39.998798 ms (FindLiveObjects: 0.100000 ms CreateObjectMapping: 0.045600 ms MarkObjects: 39.832100 ms  DeleteObjects: 0.020100 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3597.
Total: 41.835201 ms (FindLiveObjects: 0.096300 ms CreateObjectMapping: 0.045000 ms MarkObjects: 41.688999 ms  DeleteObjects: 0.004400 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3597.
Total: 40.627701 ms (FindLiveObjects: 0.102300 ms CreateObjectMapping: 0.047800 ms MarkObjects: 40.472401 ms  DeleteObjects: 0.004500 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3597.
Total: 40.921200 ms (FindLiveObjects: 0.116000 ms CreateObjectMapping: 0.049700 ms MarkObjects: 40.750999 ms  DeleteObjects: 0.003800 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3597.
Total: 40.623402 ms (FindLiveObjects: 0.098100 ms CreateObjectMapping: 0.040400 ms MarkObjects: 40.479298 ms  DeleteObjects: 0.004900 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3597.
Total: 40.035500 ms (FindLiveObjects: 0.118700 ms CreateObjectMapping: 0.065800 ms MarkObjects: 39.841400 ms  DeleteObjects: 0.009000 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3597.
Total: 43.091400 ms (FindLiveObjects: 0.097600 ms CreateObjectMapping: 0.040800 ms MarkObjects: 42.947399 ms  DeleteObjects: 0.005000 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3597.
Total: 40.646999 ms (FindLiveObjects: 0.110600 ms CreateObjectMapping: 0.045500 ms MarkObjects: 40.486599 ms  DeleteObjects: 0.003600 ms)

