#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试大文件夹拖拽功能 - 验证修复效果
"""

import os
import tempfile
import time

def create_large_test_folder():
    """创建大文件夹测试环境"""
    print("🔧 创建大文件夹测试环境...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="hyym_large_folder_test_")
    print(f"📁 测试目录: {temp_dir}")
    
    # 创建多层嵌套结构
    levels = ["level1", "level2", "level3", "level4", "level5"]
    current_path = temp_dir
    
    for level in levels:
        current_path = os.path.join(current_path, level)
        os.makedirs(current_path, exist_ok=True)
        
        # 在每层创建一些文件
        for i in range(20):
            # 创建lua文件
            lua_file = os.path.join(current_path, f"test_{level}_{i}.lua")
            with open(lua_file, 'w', encoding='utf-8') as f:
                f.write(f"""-- {level} 测试文件 {i}
function test_{level}_{i}()
    print("这是 {level} 的第 {i} 个测试文件")
    return "测试成功"
end

-- 执行测试
local result = test_{level}_{i}()
print(result)""")
            
            # 创建xml文件
            xml_file = os.path.join(current_path, f"config_{level}_{i}.xml")
            with open(xml_file, 'w', encoding='utf-8') as f:
                f.write(f"""<?xml version="1.0" encoding="UTF-8"?>
<config>
    <level>{level}</level>
    <index>{i}</index>
    <description>{level} 的第 {i} 个配置文件</description>
    <settings>
        <enabled>true</enabled>
        <priority>{i}</priority>
        <category>test</category>
    </settings>
    <metadata>
        <created>2024-08-19</created>
        <version>1.2</version>
        <author>拖拽测试</author>
    </metadata>
</config>""")
    
    # 在根目录创建大量文件
    print("📄 创建大量测试文件...")
    for i in range(200):
        # 创建lua文件
        lua_file = os.path.join(temp_dir, f"bulk_test_{i}.lua")
        with open(lua_file, 'w', encoding='utf-8') as f:
            f.write(f"""-- 批量测试文件 {i}
function bulkTest{i}()
    local data = {{
        id = {i},
        name = "bulk_test_{i}",
        type = "lua_script",
        status = "active"
    }}
    return data
end

-- 测试执行
local result = bulkTest{i}()
print("ID:", result.id)
print("名称:", result.name)
print("类型:", result.type)
print("状态:", result.status)""")
        
        # 每50个文件创建一个xml文件
        if i % 50 == 0:
            xml_file = os.path.join(temp_dir, f"bulk_config_{i}.xml")
            with open(xml_file, 'w', encoding='utf-8') as f:
                f.write(f"""<?xml version="1.0" encoding="UTF-8"?>
<bulkConfig>
    <id>{i}</id>
    <name>bulk_config_{i}</name>
    <type>xml_config</type>
    <batch>large_folder_test</batch>
    <files>
        <lua>bulk_test_{i}.lua</lua>
        <lua>bulk_test_{i+1}.lua</lua>
        <lua>bulk_test_{i+2}.lua</lua>
    </files>
</bulkConfig>""")
    
    # 创建一些特殊文件夹
    special_dirs = ["scripts", "configs", "data", "temp", "backup"]
    for dir_name in special_dirs:
        special_path = os.path.join(temp_dir, dir_name)
        os.makedirs(special_path, exist_ok=True)
        
        # 在特殊文件夹中创建文件
        for i in range(30):
            if dir_name in ["scripts"]:
                file_path = os.path.join(special_path, f"{dir_name}_script_{i}.lua")
                content = f"-- {dir_name} 脚本 {i}\nprint('{dir_name} 脚本 {i} 执行成功')"
            else:
                file_path = os.path.join(special_path, f"{dir_name}_config_{i}.xml")
                content = f"<?xml version='1.0'?><{dir_name}><id>{i}</id></{dir_name}>"
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
    
    return temp_dir

def count_files_in_folder(folder_path):
    """统计文件夹中的文件数量"""
    total_files = 0
    lua_files = 0
    xml_files = 0
    total_dirs = 0
    
    for root, dirs, files in os.walk(folder_path):
        total_dirs += len(dirs)
        for file in files:
            total_files += 1
            if file.endswith('.lua'):
                lua_files += 1
            elif file.endswith('.xml'):
                xml_files += 1
    
    return total_files, lua_files, xml_files, total_dirs

def print_test_instructions(test_folder):
    """打印测试说明"""
    total_files, lua_files, xml_files, total_dirs = count_files_in_folder(test_folder)
    
    print("\n" + "="*70)
    print("🧪 大文件夹拖拽测试指南")
    print("="*70)
    
    print(f"\n📊 测试文件夹统计:")
    print(f"  📁 总文件夹数: {total_dirs}")
    print(f"  📄 总文件数: {total_files}")
    print(f"  🔧 Lua文件数: {lua_files}")
    print(f"  ⚙️ XML文件数: {xml_files}")
    print(f"  📍 测试路径: {test_folder}")
    
    print(f"\n🎯 测试目标:")
    print(f"  ✅ 验证大文件夹拖拽不会卡死")
    print(f"  ✅ 验证异步扫描功能正常")
    print(f"  ✅ 验证扫描限制机制有效")
    print(f"  ✅ 验证用户反馈及时准确")
    
    print(f"\n🔍 测试场景:")
    
    print(f"\n🔸 场景1: 拖拽整个测试文件夹")
    print(f"   操作: 拖拽 {os.path.basename(test_folder)}")
    print(f"   预期: 立即响应，不卡死")
    print(f"   预期: 显示'正在分析文件夹内容...'")
    print(f"   预期: 显示部分扫描结果 (最多1000个文件)")
    print(f"   预期: 自动选择'全部文件'类型")
    
    print(f"\n🔸 场景2: 拖拽子文件夹")
    print(f"   操作: 拖拽 scripts 文件夹")
    print(f"   预期: 快速完成扫描")
    print(f"   预期: 自动选择'Lua文件(.lua)'类型")
    
    print(f"\n🔸 场景3: 拖拽深层嵌套文件夹")
    print(f"   操作: 拖拽 level1 文件夹")
    print(f"   预期: 限制扫描深度到3层")
    print(f"   预期: 显示深度限制提示")
    
    print(f"\n✅ 验证要点:")
    print(f"  • 程序响应时间 < 1秒")
    print(f"  • 显示实时扫描进度")
    print(f"  • 文件统计信息准确")
    print(f"  • 没有程序卡死或崩溃")
    print(f"  • 错误处理友好提示")
    
    print(f"\n⚠️ 注意事项:")
    print(f"  • 超过1000个文件会显示部分扫描提示")
    print(f"  • 超过3层深度会停止深入扫描")
    print(f"  • 观察日志区域的实时反馈")
    print(f"  • 验证界面不会冻结")
    
    print(f"\n🚀 开始测试:")
    print(f"  1. 运行: python hyym_crypto_tool.py 或双击exe文件")
    print(f"  2. 按照上述场景测试拖拽功能")
    print(f"  3. 观察程序响应和日志输出")
    print(f"  4. 验证修复效果")
    
    print("="*70)

def main():
    """主函数"""
    print("🧪 红颜一梦加密解密工具 - 大文件夹拖拽测试")
    print("="*70)
    
    print("📝 测试目的: 验证拖拽文件夹卡死问题已修复")
    
    # 创建大文件夹测试环境
    test_folder = create_large_test_folder()
    
    # 打印测试说明
    print_test_instructions(test_folder)
    
    print(f"\n🎉 大文件夹测试环境创建完成！")
    print(f"📁 测试文件夹: {test_folder}")
    print(f"\n⚡ 现在可以测试拖拽功能了！")
    
    print(f"\n💡 测试提示:")
    print(f"  • 修复前: 拖拽大文件夹会卡死")
    print(f"  • 修复后: 拖拽大文件夹立即响应")
    print(f"  • 观察日志: 显示扫描进度和限制提示")
    print(f"  • 验证稳定: 程序不会崩溃或无响应")

if __name__ == "__main__":
    main()
