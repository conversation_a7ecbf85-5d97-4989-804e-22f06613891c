#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试拖拽功能的完整实现
"""

import os
import sys
import tempfile
import time
from hyym_crypto_tool import CryptoGUI, DND_AVAILABLE

def create_test_environment():
    """创建测试环境"""
    print("🔧 创建测试环境...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="hyym_drag_test_")
    print(f"📁 测试目录: {temp_dir}")
    
    # 创建测试lua文件
    lua_content = """--[[
红颜一梦 - 拖拽功能测试
这是一个用于测试拖拽功能的Lua文件
]]--

function dragTest()
    print("拖拽功能测试成功！")
    local info = {
        feature = "Windows原生拖拽",
        version = "v1.2",
        status = "已启用"
    }
    return info
end

-- 执行测试
local result = dragTest()
print("功能:", result.feature)
print("版本:", result.version)
print("状态:", result.status)"""
    
    lua_file = os.path.join(temp_dir, "drag_test.lua")
    with open(lua_file, 'w', encoding='utf-8') as f:
        f.write(lua_content)
    
    # 创建测试xml文件
    xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<dragTest>
    <metadata>
        <title>拖拽功能测试XML</title>
        <version>1.2</version>
        <feature>Windows原生拖拽支持</feature>
    </metadata>
    <configuration>
        <dragEnabled>true</dragEnabled>
        <autoModeSwitch>true</autoModeSwitch>
        <supportedTypes>
            <type extension="lua">Lua脚本文件</type>
            <type extension="xml">XML配置文件</type>
        </supportedTypes>
    </configuration>
    <testScenarios>
        <scenario id="1">拖拽单个Lua文件</scenario>
        <scenario id="2">拖拽单个XML文件</scenario>
        <scenario id="3">拖拽包含多个文件的文件夹</scenario>
        <scenario id="4">自动模式切换验证</scenario>
    </testScenarios>
</dragTest>"""
    
    xml_file = os.path.join(temp_dir, "drag_test.xml")
    with open(xml_file, 'w', encoding='utf-8') as f:
        f.write(xml_content)
    
    # 创建子文件夹和更多测试文件
    sub_dir = os.path.join(temp_dir, "scripts")
    os.makedirs(sub_dir, exist_ok=True)
    
    # 在子文件夹中创建多个lua文件
    for i in range(3):
        sub_lua_content = f"""-- 子文件夹测试文件 {i+1}
function subDragTest{i+1}()
    return "子文件夹拖拽测试 {i+1} 成功"
end

print(subDragTest{i+1}())"""
        
        sub_lua_file = os.path.join(sub_dir, f"sub_drag_test_{i+1}.lua")
        with open(sub_lua_file, 'w', encoding='utf-8') as f:
            f.write(sub_lua_content)
    
    # 创建混合文件夹
    mixed_dir = os.path.join(temp_dir, "mixed_files")
    os.makedirs(mixed_dir, exist_ok=True)
    
    # 在混合文件夹中创建lua和xml文件
    mixed_lua = os.path.join(mixed_dir, "mixed_test.lua")
    with open(mixed_lua, 'w', encoding='utf-8') as f:
        f.write("-- 混合文件夹中的Lua文件\nprint('混合文件夹测试')")
    
    mixed_xml = os.path.join(mixed_dir, "mixed_test.xml")
    with open(mixed_xml, 'w', encoding='utf-8') as f:
        f.write("<?xml version='1.0'?><test>混合文件夹测试</test>")
    
    return {
        'temp_dir': temp_dir,
        'lua_file': lua_file,
        'xml_file': xml_file,
        'sub_dir': sub_dir,
        'mixed_dir': mixed_dir
    }

def print_test_instructions(test_env):
    """打印测试说明"""
    print("\n" + "="*70)
    print("🎯 拖拽功能测试指南")
    print("="*70)
    
    print(f"\n📋 测试环境信息:")
    print(f"  📁 主测试目录: {test_env['temp_dir']}")
    print(f"  📄 Lua测试文件: {os.path.basename(test_env['lua_file'])}")
    print(f"  📄 XML测试文件: {os.path.basename(test_env['xml_file'])}")
    print(f"  📁 子目录: {os.path.basename(test_env['sub_dir'])} (3个lua文件)")
    print(f"  📁 混合目录: {os.path.basename(test_env['mixed_dir'])} (lua+xml文件)")
    
    print(f"\n🔍 拖拽功能状态:")
    if DND_AVAILABLE:
        print("  ✅ Windows API 可用")
        print("  ✅ 拖拽功能已启用")
    else:
        print("  ❌ Windows API 不可用")
        print("  ⚠️ 拖拽功能将被禁用")
    
    print(f"\n🎮 测试场景:")
    
    print(f"\n🔸 场景1: 拖拽单个Lua文件")
    print(f"   操作: 拖拽 {test_env['lua_file']}")
    print(f"   预期: 自动切换到'单独处理文件'模式")
    print(f"   预期: 识别为Lua文件类型")
    
    print(f"\n🔸 场景2: 拖拽单个XML文件")
    print(f"   操作: 拖拽 {test_env['xml_file']}")
    print(f"   预期: 自动切换到'单独处理文件'模式")
    print(f"   预期: 识别为XML文件类型")
    
    print(f"\n🔸 场景3: 拖拽纯Lua文件夹")
    print(f"   操作: 拖拽 {test_env['sub_dir']}")
    print(f"   预期: 自动切换到'批量处理文件夹'模式")
    print(f"   预期: 自动选择'Lua文件(.lua)'类型")
    
    print(f"\n🔸 场景4: 拖拽混合文件夹")
    print(f"   操作: 拖拽 {test_env['mixed_dir']}")
    print(f"   预期: 自动切换到'批量处理文件夹'模式")
    print(f"   预期: 自动选择'全部文件'类型")
    
    print(f"\n🔸 场景5: 拖拽主测试目录")
    print(f"   操作: 拖拽 {test_env['temp_dir']}")
    print(f"   预期: 自动切换到'批量处理文件夹'模式")
    print(f"   预期: 自动选择'全部文件'类型")
    
    print(f"\n✅ 验证要点:")
    print(f"  • 拖拽时日志显示正确路径")
    print(f"  • 模式自动切换正确")
    print(f"  • 文件类型识别和选择正确")
    print(f"  • 界面元素显示/隐藏正确")
    print(f"  • 路径输入框自动填充")
    print(f"  • 文件统计信息准确")
    
    print(f"\n💡 测试技巧:")
    print(f"  • 观察程序日志中的实时反馈")
    print(f"  • 验证界面元素的自动调整")
    print(f"  • 测试不同文件类型的识别")
    print(f"  • 检查错误处理和提示信息")
    
    print(f"\n🚀 开始测试:")
    print(f"  1. 运行命令: python hyym_crypto_tool.py")
    print(f"  2. 按照上述场景逐一测试拖拽功能")
    print(f"  3. 观察程序响应和日志输出")
    print(f"  4. 验证所有预期行为")
    
    print("="*70)

def check_system_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    # 检查操作系统
    if sys.platform != 'win32':
        print("❌ 拖拽功能仅支持Windows系统")
        return False
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 需要Python 3.6或更高版本")
        return False
    
    # 检查必要的库
    try:
        import tkinter
        print("✅ tkinter 可用")
    except ImportError:
        print("❌ tkinter 不可用")
        return False
    
    try:
        import ctypes
        print("✅ ctypes 可用")
    except ImportError:
        print("❌ ctypes 不可用")
        return False
    
    # 检查Windows API
    if DND_AVAILABLE:
        print("✅ Windows拖拽API 可用")
    else:
        print("⚠️ Windows拖拽API 不可用，拖拽功能将被禁用")
    
    print("✅ 系统要求检查完成")
    return True

def main():
    """主函数"""
    print("🎯 红颜一梦加密解密工具 - 拖拽功能测试")
    print("="*70)
    
    # 检查系统要求
    if not check_system_requirements():
        print("❌ 系统要求不满足，无法进行测试")
        return
    
    # 创建测试环境
    test_env = create_test_environment()
    
    # 打印测试说明
    print_test_instructions(test_env)
    
    print(f"\n🎉 测试环境准备完成！")
    print(f"📁 测试文件位置: {test_env['temp_dir']}")
    print(f"\n⚡ 现在可以启动程序进行拖拽功能测试了！")

if __name__ == "__main__":
    main()
