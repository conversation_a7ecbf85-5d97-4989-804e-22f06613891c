2025-08-19 23:06:31,870 - INFO - 诊断开始: 2025-08-19 23:06:31.870384
2025-08-19 23:06:31,897 - INFO - 开始测试拖拽相关函数...
2025-08-19 23:06:32,003 - INFO - DND_AVAILABLE: True
2025-08-19 23:06:32,003 - INFO - Windows API 可用
2025-08-19 23:06:32,003 - INFO - 桌面窗口句柄: 65548
2025-08-19 23:06:32,003 - INFO - 测试GUI创建...
2025-08-19 23:06:32,070 - INFO - Tkinter根窗口创建成功
2025-08-19 23:06:32,072 - ERROR - CryptoGUI创建失败: CryptoGUI.__init__() takes 1 positional argument but 2 were given
2025-08-19 23:06:32,072 - ERROR - Traceback (most recent call last):
  File "D:\Thunderbolt cloud disk\HYYM\debug_drag_crash.py", line 112, in test_gui_creation
    gui = CryptoGUI(root)
          ^^^^^^^^^^^^^^^
TypeError: CryptoGUI.__init__() takes 1 positional argument but 2 were given

2025-08-19 23:06:32,082 - INFO - 创建简单测试文件...
2025-08-19 23:06:32,082 - INFO - 测试目录: C:\Users\<USER>\AppData\Local\Temp\drag_debug_4eysw5va
2025-08-19 23:06:32,087 - INFO - 创建了测试文件: C:\Users\<USER>\AppData\Local\Temp\drag_debug_4eysw5va\simple_test.lua, C:\Users\<USER>\AppData\Local\Temp\drag_debug_4eysw5va\simple_test.xml, C:\Users\<USER>\AppData\Local\Temp\drag_debug_4eysw5va\subfolder\sub_test.lua
2025-08-19 23:06:32,087 - INFO - 运行最小化拖拽测试...
2025-08-19 23:06:32,214 - INFO - 最小化测试窗口创建成功，开始运行...
2025-08-19 23:06:37,260 - INFO - 最小化测试完成
2025-08-19 23:06:37,264 - INFO - 诊断结束: 2025-08-19 23:06:37.264131
2025-08-19 23:06:37,264 - INFO - 总耗时: 0:00:05.393747
