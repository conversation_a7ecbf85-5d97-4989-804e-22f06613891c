# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
    # Set not needed items to zero 0.
    filevers=(1,2,0,0),
    prodvers=(1,2,0,0),
    # Contains a bitmask that specifies the valid bits 'flags'r
    mask=0x3f,
    # Contains a bitmask that specifies the Boolean attributes of the file.
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x4,
    # The general type of file.
    # 0x1 - the file is an application.
    fileType=0x1,
    # The function of the file.
    # 0x0 - the function is not defined for this fileType
    subtype=0x0,
    # Creation date and time stamp.
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404B0',
        [StringStruct(u'CompanyName', u'红颜一梦游戏工具开发组'),
        StringStruct(u'FileDescription', u'红颜一梦加密解密工具 - 支持Windows原生拖拽'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'hyym_crypto_tool'),
        StringStruct(u'LegalCopyright', u'基于红颜一梦游戏源码分析制作'),
        StringStruct(u'OriginalFilename', u'红颜一梦加密解密工具_v1.2_拖拽版.exe'),
        StringStruct(u'ProductName', u'红颜一梦加密解密工具'),
        StringStruct(u'ProductVersion', u'1.2 拖拽版'),
        StringStruct(u'Comments', u'支持lua和xml文件的加密解密，具备Windows原生拖拽功能')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
