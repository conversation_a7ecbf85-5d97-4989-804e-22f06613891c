#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试拖拽功能
"""

import os
import tempfile
from hyym_crypto_tool import HYYMCrypto

def create_test_files():
    """创建测试文件"""
    print("=== 创建测试文件 ===")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="hyym_test_")
    print(f"测试目录: {temp_dir}")
    
    # 创建测试lua文件
    lua_content = """--[[
红颜一梦 - 拖拽测试Lua文件
这个文件用于测试拖拽功能
]]--

function testDragDrop()
    print("拖拽功能测试")
    local data = {
        feature = "拖拽支持",
        version = "v1.2",
        api = "Windows原生API"
    }
    return data
end

-- 测试拖拽
local result = testDragDrop()
print("功能:", result.feature)
print("版本:", result.version)
print("实现:", result.api)"""
    
    lua_file = os.path.join(temp_dir, "drag_test.lua")
    with open(lua_file, 'w', encoding='utf-8') as f:
        f.write(lua_content)
    
    # 创建测试xml文件
    xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<dragTest>
    <info>
        <title>拖拽功能测试</title>
        <version>1.2</version>
        <feature>Windows原生拖拽支持</feature>
    </info>
    <settings>
        <dragEnabled>true</dragEnabled>
        <autoSwitch>true</autoSwitch>
        <fileTypes>
            <type>lua</type>
            <type>xml</type>
        </fileTypes>
    </settings>
    <test>
        <scenario>拖拽单个文件</scenario>
        <scenario>拖拽文件夹</scenario>
        <scenario>自动模式切换</scenario>
    </test>
</dragTest>"""
    
    xml_file = os.path.join(temp_dir, "drag_test.xml")
    with open(xml_file, 'w', encoding='utf-8') as f:
        f.write(xml_content)
    
    # 创建子文件夹
    sub_dir = os.path.join(temp_dir, "lua_scripts")
    os.makedirs(sub_dir, exist_ok=True)
    
    # 在子文件夹中创建更多lua文件
    for i in range(3):
        sub_lua_content = f"""-- 子文件夹测试文件 {i+1}
function subTest{i+1}()
    return "拖拽测试子文件 {i+1}"
end"""
        sub_lua_file = os.path.join(sub_dir, f"sub_test_{i+1}.lua")
        with open(sub_lua_file, 'w', encoding='utf-8') as f:
            f.write(sub_lua_content)
    
    print(f"✅ 创建测试文件:")
    print(f"  📄 {lua_file}")
    print(f"  📄 {xml_file}")
    print(f"  📁 {sub_dir} (包含3个lua文件)")
    
    return temp_dir, lua_file, xml_file, sub_dir

def test_crypto_functions():
    """测试加密解密功能"""
    print("\n=== 测试加密解密功能 ===")
    
    crypto = HYYMCrypto()
    
    # 测试lua加密解密
    lua_text = "function test() return 'hello' end"
    print(f"原始Lua: {lua_text}")
    
    encrypted_lua = crypto.encrypt_lua_file(lua_text)
    print(f"加密后: {encrypted_lua[:50]}...")
    
    decrypted_lua = crypto.decrypt_lua_file(encrypted_lua)
    print(f"解密后: {decrypted_lua}")
    
    if decrypted_lua.strip() == lua_text.strip():
        print("✅ Lua加密解密测试通过")
    else:
        print("❌ Lua加密解密测试失败")
    
    # 测试xml加密解密
    xml_text = "<?xml version='1.0'?><test>hello</test>"
    print(f"\n原始XML: {xml_text}")
    
    encrypted_xml = crypto.encrypt_xml_file(xml_text)
    print(f"加密后: {encrypted_xml[:50]}...")
    
    decrypted_xml = crypto.decrypt_xml_file(encrypted_xml)
    print(f"解密后: {decrypted_xml}")
    
    if decrypted_xml.strip() == xml_text.strip():
        print("✅ XML加密解密测试通过")
    else:
        print("❌ XML加密解密测试失败")

def print_drag_instructions(temp_dir, lua_file, xml_file, sub_dir):
    """打印拖拽测试说明"""
    print("\n" + "="*60)
    print("🎯 拖拽功能测试说明")
    print("="*60)
    
    print("\n📋 测试步骤:")
    print("1. 运行程序: python hyym_crypto_tool.py")
    print("2. 按照以下场景测试拖拽功能:")
    
    print("\n🔸 场景1: 拖拽单个Lua文件")
    print(f"   拖拽文件: {lua_file}")
    print("   预期结果: 自动切换到'单独处理文件'模式")
    print("   预期结果: 自动识别为Lua文件类型")
    
    print("\n🔸 场景2: 拖拽单个XML文件")
    print(f"   拖拽文件: {xml_file}")
    print("   预期结果: 自动切换到'单独处理文件'模式")
    print("   预期结果: 自动识别为XML文件类型")
    
    print("\n🔸 场景3: 拖拽文件夹")
    print(f"   拖拽文件夹: {temp_dir}")
    print("   预期结果: 自动切换到'批量处理文件夹'模式")
    print("   预期结果: 显示文件类型选择选项")
    
    print("\n🔸 场景4: 拖拽子文件夹")
    print(f"   拖拽文件夹: {sub_dir}")
    print("   预期结果: 自动切换到'批量处理文件夹'模式")
    print("   预期结果: 显示文件类型选择选项")
    
    print("\n✅ 验证要点:")
    print("• 拖拽时程序日志显示正确的路径")
    print("• 模式自动切换正确")
    print("• 文件类型识别正确")
    print("• 界面元素显示/隐藏正确")
    print("• 处理功能正常工作")
    
    print("\n💡 提示:")
    print("• 如果拖拽不工作，检查程序日志中的拖拽支持状态")
    print("• 可以使用浏览按钮或Ctrl+V作为备选方案")
    print("• 测试完成后可以删除临时目录")
    
    print(f"\n📁 临时测试目录: {temp_dir}")
    print("="*60)

def main():
    """主函数"""
    print("🎯 红颜一梦加密解密工具 - 拖拽功能测试")
    print("="*60)
    
    # 测试加密解密功能
    test_crypto_functions()
    
    # 创建测试文件
    temp_dir, lua_file, xml_file, sub_dir = create_test_files()
    
    # 打印拖拽测试说明
    print_drag_instructions(temp_dir, lua_file, xml_file, sub_dir)
    
    print("\n🚀 现在可以启动程序进行拖拽测试了！")
    print("   命令: python hyym_crypto_tool.py")

if __name__ == "__main__":
    main()
