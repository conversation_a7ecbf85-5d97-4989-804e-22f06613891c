@echo off
chcp 65001 >nul
title 红颜一梦加密解密工具

echo.
echo ========================================
echo    红颜一梦 加密解密工具 v1.2
echo ========================================
echo.
echo 正在启动工具...
echo.

if exist "dist\红颜一梦加密解密工具_v1.2.exe" (
    start "" "dist\红颜一梦加密解密工具_v1.2.exe"
    echo 工具已启动！
) else (
    echo 错误：找不到工具文件！
    echo 请确保 dist\红颜一梦加密解密工具_v1.2.exe 文件存在
    echo.
    pause
)

echo.
echo 如果工具没有正常启动，请检查：
echo 1. 是否有杀毒软件拦截
echo 2. 是否有足够的系统权限
echo 3. 文件是否完整
echo.
timeout /t 3 >nul
