#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本 - 将红颜一梦加密解密工具打包成exe文件
"""

import os
import sys
import subprocess
import shutil

def install_requirements():
    """安装依赖"""
    print("正在安装依赖...")
    script_dir = os.path.dirname(os.path.abspath(__file__))
    requirements_path = os.path.join(script_dir, "requirements.txt")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", requirements_path])
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])

def create_icon():
    """创建专业的图标文件"""
    # 这里我们创建一个更专业的图标
    icon_script = '''
from PIL import Image, ImageDraw, ImageFont
import os

# 创建多个尺寸的图标
sizes = [(16, 16), (32, 32), (48, 48), (64, 64)]
images = []

for size in sizes:
    img = Image.new('RGBA', size, (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)

    # 根据尺寸调整绘制参数
    w, h = size
    scale = w / 32.0

    # 绘制红颜一梦主题的图标
    # 背景圆形
    margin = int(2 * scale)
    draw.ellipse([margin, margin, w-margin, h-margin],
                fill=(220, 20, 60, 255), outline=(139, 0, 0, 255), width=max(1, int(2*scale)))

    # 内部装饰
    center_x, center_y = w//2, h//2
    inner_radius = int(8 * scale)

    # 绘制中心的"红"字样式装饰
    if w >= 32:
        # 绘制简化的中文字符样式
        line_width = max(1, int(2 * scale))
        # 横线
        draw.line([center_x-inner_radius//2, center_y-inner_radius//3,
                  center_x+inner_radius//2, center_y-inner_radius//3],
                 fill=(255, 255, 255, 255), width=line_width)
        # 竖线
        draw.line([center_x, center_y-inner_radius//2,
                  center_x, center_y+inner_radius//2],
                 fill=(255, 255, 255, 255), width=line_width)
        # 装饰点
        draw.ellipse([center_x-2, center_y+inner_radius//3-2,
                     center_x+2, center_y+inner_radius//3+2],
                    fill=(255, 255, 255, 255))
    else:
        # 小尺寸时绘制简单的点
        draw.ellipse([center_x-2, center_y-2, center_x+2, center_y+2],
                    fill=(255, 255, 255, 255))

    images.append(img)

# 保存为ICO文件
images[0].save('icon.ico', format='ICO', sizes=[(img.width, img.height) for img in images],
               append_images=images[1:])
print("专业图标文件已创建: icon.ico")
'''
    
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)  # 切换到脚本目录

        # 尝试使用PIL创建图标
        subprocess.check_call([sys.executable, "-m", "pip", "install", "Pillow"])
        icon_script_path = os.path.join(script_dir, 'create_icon.py')
        with open(icon_script_path, 'w', encoding='utf-8') as f:
            f.write(icon_script)
        subprocess.check_call([sys.executable, icon_script_path])
        if os.path.exists(icon_script_path):
            os.remove(icon_script_path)
    except:
        print("无法创建图标文件，将使用默认图标")

def build_exe():
    """构建exe文件"""
    print("🔨 正在构建exe文件...")

    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)  # 切换到脚本目录

    # PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",  # 打包成单个文件
        "--windowed",  # 不显示控制台窗口
        "--name=红颜一梦加密解密工具_v1.2_稳定版",  # 输出文件名
        "--distpath=dist",  # 输出目录
        "--workpath=build",  # 临时文件目录
        "--specpath=.",  # spec文件位置
        "--clean",  # 清理缓存
        "--noconfirm",  # 不询问覆盖
        # 优化选项
        "--optimize=2",  # Python优化级别
        "--strip",  # 去除调试信息
        # 优化和清理选项
        "--exclude-module=matplotlib",  # 排除不需要的模块
        "--exclude-module=numpy",
        "--exclude-module=pandas"
    ]

    # 如果图标文件存在，添加图标参数
    icon_path = os.path.join(script_dir, 'icon.ico')
    if os.path.exists(icon_path):
        cmd.extend([f"--icon={icon_path}"])
        print("✅ 使用自定义图标")
    else:
        print("⚠️ 未找到图标文件，使用默认图标")

    # 在Windows上添加版本信息
    if sys.platform == 'win32':
        version_file = os.path.join(script_dir, 'version_info.txt')
        if os.path.exists(version_file):
            cmd.extend([f"--version-file={version_file}"])
            print("✅ 添加版本信息")

    # 添加主文件
    main_file = os.path.join(script_dir, "hyym_crypto_tool.py")
    cmd.append(main_file)

    print("📦 开始打包...")
    print(f"📄 主文件: {main_file}")

    # 执行打包命令
    try:
        subprocess.check_call(cmd)
        print("✅ 构建完成!")
        exe_path = os.path.abspath('dist/红颜一梦加密解密工具_v1.2_稳定版.exe')
        print(f"📁 exe文件位置: {exe_path}")

        # 检查文件大小
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"📊 文件大小: {file_size:.1f} MB")

    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        raise

def clean_build():
    """清理构建文件"""
    print("🧹 清理构建文件...")

    # 清理文件夹
    for folder in ['build', '__pycache__']:
        if os.path.exists(folder):
            shutil.rmtree(folder)
            print(f"✅ 已清理文件夹: {folder}")

    # 清理临时文件
    temp_files = [
        '红颜一梦加密解密工具_v1.2_稳定版.spec',
        'create_icon.py'
    ]

    for file in temp_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"✅ 已清理文件: {file}")

    print("🎯 清理完成")

def main():
    """主函数"""
    print("=== 红颜一梦加密解密工具 v1.2 拖拽版 打包脚本 ===")
    
    try:
        # 1. 安装依赖
        install_requirements()
        
        # 2. 创建图标
        create_icon()
        
        # 3. 构建exe
        build_exe()
        
        # 4. 清理文件
        clean_build()
        
        print("\n=== 打包完成! ===")
        print("🎉 红颜一梦加密解密工具 v1.2 拖拽版 已成功打包!")
        print("📁 你可以在 dist 文件夹中找到生成的exe文件")
        print("🚀 该文件可以独立运行，无需安装Python环境")
        print("✨ 新功能: 支持Windows原生拖拽操作!")
        
    except subprocess.CalledProcessError as e:
        print(f"构建过程中出错: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"未知错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
