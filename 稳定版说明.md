# 红颜一梦加密解密工具 v1.2 稳定版说明

## 🎯 稳定版特色

v1.2稳定版是专门为解决兼容性问题而优化的版本。我们移除了可能导致程序崩溃或闪退的复杂功能，专注于提供稳定可靠的核心功能。

## ✅ 核心功能

### 完整的加密解密支持
- **Lua文件**：支持CRC格式和ExtractString格式的加密解密
- **XML文件**：支持CRC加密格式的加密解密
- **自动识别**：智能识别文件的加密状态
- **直接覆盖**：处理后直接覆盖原文件

### 双模式处理
- **单文件处理**：精确处理单个lua或xml文件
- **批量处理**：一次性处理整个文件夹中的所有符合条件文件
- **智能切换**：根据选择内容自动切换处理模式
- **文件类型识别**：单文件模式下自动识别文件类型

### 便捷操作
- **浏览选择**：点击浏览按钮选择文件或文件夹
- **快捷粘贴**：支持Ctrl+V粘贴文件路径
- **实时日志**：详细的处理过程和状态反馈
- **进度显示**：批量处理时显示处理进度

## 🔧 稳定性优化

### 移除的复杂功能
- ❌ **拖拽API调用**：移除了可能导致崩溃的Windows拖拽API
- ❌ **窗口过程钩子**：移除了复杂的窗口消息处理
- ❌ **第三方拖拽库**：移除了tkinterdnd2等可能有兼容性问题的库
- ❌ **复杂的异步操作**：简化了多线程处理逻辑

### 增强的稳定性
- ✅ **异常处理**：全面的try-catch错误处理
- ✅ **内存管理**：优化内存使用，避免内存泄漏
- ✅ **资源清理**：确保程序退出时正确清理资源
- ✅ **兼容性检查**：启动时检查系统兼容性

## 🖥️ 系统兼容性

### 支持的操作系统
- **Windows 7** (SP1及以上)
- **Windows 8/8.1**
- **Windows 10** (所有版本)
- **Windows 11** (所有版本)

### 系统要求
- **处理器**：x86或x64架构
- **内存**：最少512MB RAM
- **存储空间**：50MB可用空间
- **权限**：普通用户权限（推荐管理员权限）

### 依赖要求
- **无需Python环境**：独立可执行文件
- **无需额外库**：所有依赖已打包
- **无需注册表**：绿色软件，无需安装

## 📋 使用指南

### 快速开始
1. **解压文件**：将发布包解压到任意目录
2. **启动程序**：双击exe文件或运行工具.bat
3. **选择模式**：根据需要选择单文件或批量处理
4. **选择文件**：使用浏览按钮或Ctrl+V粘贴路径
5. **开始处理**：选择操作类型后点击开始处理

### 操作技巧
- **备份文件**：处理前务必备份重要文件
- **路径复制**：可以从文件管理器复制路径后粘贴
- **批量选择**：批量模式下可以选择文件类型
- **日志查看**：通过日志了解处理状态和结果

## 🛠️ 故障排除

### 程序无法启动
```
可能原因：
1. 系统版本过低
2. 缺少必要的系统组件
3. 被杀毒软件拦截

解决方案：
1. 确保Windows版本为7及以上
2. 安装最新的Visual C++运行库
3. 将程序添加到杀毒软件白名单
4. 尝试以管理员身份运行
```

### 处理失败
```
可能原因：
1. 文件格式不正确
2. 文件被其他程序占用
3. 权限不足

解决方案：
1. 确认文件为游戏原版格式
2. 关闭可能占用文件的程序
3. 以管理员身份运行程序
4. 检查文件是否损坏
```

### 中文显示异常
```
可能原因：
1. 系统编码设置问题
2. 字体缺失

解决方案：
1. 设置系统区域为中国
2. 安装微软雅黑字体
3. 检查系统语言设置
```

## 📊 性能特点

### 处理速度
- **单文件**：通常在1秒内完成
- **小批量**：10个文件约3-5秒
- **大批量**：100个文件约30-60秒
- **内存占用**：通常不超过100MB

### 文件支持
- **最大文件大小**：理论上无限制，实际建议不超过100MB
- **文件数量**：批量处理建议不超过1000个文件
- **路径长度**：支持Windows标准路径长度
- **文件名**：支持中文和特殊字符

## 🎉 版本优势

### 相比v1.1的改进
- ✅ 新增单文件处理功能
- ✅ 智能模式切换
- ✅ 更好的用户界面
- ✅ 增强的错误处理
- ✅ 提高的稳定性

### 相比拖拽版的优势
- ✅ 更高的稳定性
- ✅ 更好的兼容性
- ✅ 更少的崩溃风险
- ✅ 更简单的操作
- ✅ 更可靠的运行

## 💡 使用建议

### 最佳实践
1. **定期备份**：处理重要文件前务必备份
2. **小批量测试**：大批量处理前先测试少量文件
3. **权限确认**：确保程序有足够的文件访问权限
4. **路径检查**：确保文件路径不包含特殊字符
5. **版本确认**：确保处理的是游戏原版文件

### 注意事项
- ⚠️ **不可逆操作**：加密解密会直接覆盖原文件
- ⚠️ **格式要求**：只能处理符合游戏格式的文件
- ⚠️ **权限需要**：某些系统目录可能需要管理员权限
- ⚠️ **杀毒软件**：可能需要添加到白名单

## 📞 技术支持

### 获取帮助
1. 查看使用说明.md获取详细操作指南
2. 查看问题解决方案.md获取常见问题解答
3. 使用示例文件测试程序功能
4. 通过相关渠道反馈问题

### 反馈信息
如需技术支持，请提供：
- 操作系统版本和位数
- 程序版本信息
- 错误信息截图
- 操作步骤描述
- 问题文件（如可能）

---
**开发者**：基于红颜一梦游戏源码分析制作  
**版本**：v1.2 稳定版  
**发布日期**：2024年8月19日  
**特色**：专注稳定性，确保在各种环境下可靠运行
