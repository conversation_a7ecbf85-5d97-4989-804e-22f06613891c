#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建发布包脚本
将所有必要文件整理到发布文件夹中
"""

import os
import shutil
import zipfile
from datetime import datetime

def create_release_package():
    """创建发布包"""
    print("=== 创建红颜一梦加密解密工具发布包 ===")
    
    # 发布文件夹名称
    release_name = "红颜一梦加密解密工具_v1.2_稳定版"
    release_dir = f"release/{release_name}"

    # 创建发布目录
    os.makedirs(release_dir, exist_ok=True)
    
    print(f"创建发布目录: {release_dir}")
    
    # 需要复制的文件列表
    files_to_copy = [
        ("dist/红颜一梦加密解密工具_v1.2.exe", "红颜一梦加密解密工具_v1.2.exe"),
        ("使用说明.md", "使用说明.md"),
        ("运行工具.bat", "运行工具.bat"),
        ("发布说明.txt", "发布说明.txt"),
        ("问题解决方案.md", "问题解决方案.md"),
    ]
    
    # 复制文件
    for src, dst in files_to_copy:
        if os.path.exists(src):
            dst_path = os.path.join(release_dir, dst)
            shutil.copy2(src, dst_path)
            print(f"✅ 复制: {src} -> {dst}")
        else:
            print(f"❌ 文件不存在: {src}")
    
    # 创建示例文件夹
    example_dir = os.path.join(release_dir, "示例文件")
    os.makedirs(example_dir, exist_ok=True)
    
    # 复制示例文件
    example_files = [
        ("gamedata/suyu/HYYM/lua/AI.lua", "示例文件/AI_encrypted.lua"),
        ("gamedata/suyu/HYYM/lua/AI_decrypted.lua", "示例文件/AI_decrypted.lua"),
        ("gamedata/suyu/HYYM/Scripts/touch.xml", "示例文件/touch_encrypted.xml"),
        ("gamedata/suyu/HYYM/Scripts/touch_decrypted.xml", "示例文件/touch_decrypted.xml"),
    ]
    
    for src, dst in example_files:
        if os.path.exists(src):
            dst_path = os.path.join(release_dir, dst)
            shutil.copy2(src, dst_path)
            print(f"✅ 复制示例: {src} -> {dst}")
    
    # 创建示例说明文件
    example_readme = os.path.join(example_dir, "示例说明.txt")
    with open(example_readme, 'w', encoding='utf-8') as f:
        f.write("""示例文件说明
===========

本文件夹包含了加密和解密的示例文件，供测试和参考使用：

Lua文件示例：
- AI_encrypted.lua  : 加密的lua文件（游戏原始格式）
- AI_decrypted.lua  : 解密后的lua文件（可读格式）

XML文件示例：
- touch_encrypted.xml : 加密的xml文件（游戏原始格式）
- touch_decrypted.xml : 解密后的xml文件（可读格式）

使用方法：
1. 将示例文件复制到一个测试文件夹中
2. 使用工具对加密文件进行解密测试
3. 使用工具对明文文件进行加密测试

注意：这些文件来自《红颜一梦》游戏，仅供测试使用。
""")
    
    print(f"✅ 创建示例说明: {example_readme}")
    
    # 创建压缩包
    zip_name = f"release/{release_name}.zip"
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(release_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, "release")
                zipf.write(file_path, arc_name)
    
    print(f"✅ 创建压缩包: {zip_name}")
    
    # 显示发布包信息
    print("\n=== 发布包创建完成 ===")
    print(f"📁 发布目录: {release_dir}")
    print(f"📦 压缩包: {zip_name}")
    
    # 计算文件大小
    exe_size = os.path.getsize("dist/红颜一梦加密解密工具_v1.2.exe") / (1024 * 1024)
    zip_size = os.path.getsize(zip_name) / (1024 * 1024)
    
    print(f"📊 EXE文件大小: {exe_size:.1f} MB")
    print(f"📊 压缩包大小: {zip_size:.1f} MB")
    
    print("\n📋 发布包内容:")
    for root, dirs, files in os.walk(release_dir):
        level = root.replace(release_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}📁 {os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}📄 {file}")
    
    print(f"\n🎉 发布包已准备就绪！")
    print(f"你可以将 {zip_name} 分享给其他用户")

if __name__ == "__main__":
    create_release_package()
