#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试v1.1版本的新功能
"""

import os
import shutil
import tempfile
from hyym_crypto_tool import HYYMCrypto

def test_overwrite_functionality():
    """测试直接覆盖原文件的功能"""
    print("=== 测试v1.1版本新功能 ===\n")
    
    crypto = HYYMCrypto()
    
    # 创建临时测试目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"创建临时测试目录: {temp_dir}")
        
        # 测试文本
        test_content = """--[[
这是一个测试Lua文件
用于验证加密解密功能
]]--

function test()
    print("Hello, 红颜一梦!")
    return "success"
end

local data = {
    name = "测试",
    value = 123
}"""
        
        # 创建测试文件
        test_file = os.path.join(temp_dir, "test.lua")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"✅ 创建测试文件: {test_file}")
        print(f"原始内容长度: {len(test_content)} 字符")
        
        # 1. 测试加密（覆盖原文件）
        print("\n1. 测试加密功能...")
        encrypted_content = crypto.encrypt_lua_file(test_content)
        
        # 模拟覆盖原文件
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(encrypted_content)
        
        # 读取加密后的文件
        with open(test_file, 'r', encoding='utf-8') as f:
            file_encrypted = f.read()
        
        print(f"✅ 加密成功，文件已覆盖")
        print(f"加密后内容长度: {len(file_encrypted)} 字符")
        print(f"加密内容预览: {file_encrypted[:100]}...")
        
        # 2. 测试解密（覆盖原文件）
        print("\n2. 测试解密功能...")
        decrypted_content = crypto.decrypt_lua_file(file_encrypted)
        
        # 模拟覆盖原文件
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(decrypted_content)
        
        # 读取解密后的文件
        with open(test_file, 'r', encoding='utf-8') as f:
            file_decrypted = f.read()
        
        print(f"✅ 解密成功，文件已覆盖")
        print(f"解密后内容长度: {len(file_decrypted)} 字符")
        print(f"解密内容预览: {file_decrypted[:100]}...")
        
        # 3. 验证内容一致性
        print("\n3. 验证内容一致性...")
        if file_decrypted.strip() == test_content.strip():
            print("✅ 内容一致性验证通过！")
            print("✅ 加密解密循环测试成功！")
        else:
            print("❌ 内容一致性验证失败！")
            print(f"原始长度: {len(test_content)}")
            print(f"解密长度: {len(file_decrypted)}")
        
        print(f"\n📁 测试文件最终状态: {test_file}")
        print("🎉 v1.1版本覆盖功能测试完成！")

def test_drag_drop_simulation():
    """模拟拖拽功能测试"""
    print("\n=== 模拟拖拽功能测试 ===")
    
    # 模拟拖拽路径处理
    test_paths = [
        "D:\\Game\\红颜一梦\\lua",
        "D:\\Game\\红颜一梦\\Scripts",
        "D:\\Game\\红颜一梦\\test.lua",  # 文件路径
    ]
    
    for path in test_paths:
        print(f"\n模拟拖拽路径: {path}")
        
        if path.endswith('.lua') or path.endswith('.xml'):
            # 如果是文件，取父目录
            parent_dir = os.path.dirname(path)
            print(f"  -> 检测到文件，使用父目录: {parent_dir}")
        else:
            # 如果是目录，直接使用
            print(f"  -> 检测到目录，直接使用: {path}")
    
    print("✅ 拖拽路径处理逻辑测试完成！")

if __name__ == "__main__":
    test_overwrite_functionality()
    test_drag_drop_simulation()
    
    print("\n" + "="*50)
    print("🎉 v1.1版本所有新功能测试完成！")
    print("📋 新功能总结:")
    print("  ✅ 支持拖拽文件夹到程序界面")
    print("  ✅ 处理后直接覆盖原文件")
    print("  ✅ 优化的用户界面和提示")
    print("  ✅ 更好的用户体验")
    print("="*50)
