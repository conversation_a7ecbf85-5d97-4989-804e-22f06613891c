#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试v1.2版本的新功能
"""

import os
import shutil
import tempfile
from hyym_crypto_tool import HYYMCrypto

def test_single_file_processing():
    """测试单文件处理功能"""
    print("=== 测试v1.2版本单文件处理功能 ===\n")
    
    crypto = HYYMCrypto()
    
    # 创建临时测试目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"创建临时测试目录: {temp_dir}")
        
        # 测试Lua文件
        lua_content = """--[[
这是一个测试Lua文件
用于验证单文件加密解密功能
]]--

function testFunction()
    print("Hello, 红颜一梦!")
    local data = {
        name = "测试数据",
        value = 12345,
        flag = true
    }
    return data
end

-- 测试注释
local result = testFunction()
print("结果:", result.name)"""
        
        # 创建测试Lua文件
        lua_file = os.path.join(temp_dir, "test_single.lua")
        with open(lua_file, 'w', encoding='utf-8') as f:
            f.write(lua_content)
        
        print(f"✅ 创建测试Lua文件: {lua_file}")
        print(f"原始内容长度: {len(lua_content)} 字符")
        
        # 1. 测试Lua文件加密
        print("\n1. 测试Lua文件加密...")
        encrypted_lua = crypto.encrypt_lua_file(lua_content)
        
        # 模拟单文件加密处理
        with open(lua_file, 'w', encoding='utf-8') as f:
            f.write(encrypted_lua)
        
        with open(lua_file, 'r', encoding='utf-8') as f:
            file_encrypted = f.read()
        
        print(f"✅ 加密成功，文件已覆盖")
        print(f"加密后内容长度: {len(file_encrypted)} 字符")
        print(f"加密内容预览: {file_encrypted[:80]}...")
        
        # 2. 测试Lua文件解密
        print("\n2. 测试Lua文件解密...")
        decrypted_lua = crypto.decrypt_lua_file(file_encrypted)
        
        # 模拟单文件解密处理
        with open(lua_file, 'w', encoding='utf-8') as f:
            f.write(decrypted_lua)
        
        with open(lua_file, 'r', encoding='utf-8') as f:
            file_decrypted = f.read()
        
        print(f"✅ 解密成功，文件已覆盖")
        print(f"解密后内容长度: {len(file_decrypted)} 字符")
        
        # 3. 验证内容一致性
        print("\n3. 验证Lua文件内容一致性...")
        if file_decrypted.strip() == lua_content.strip():
            print("✅ Lua文件内容一致性验证通过！")
        else:
            print("❌ Lua文件内容一致性验证失败！")
        
        # 测试XML文件
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<root>
    <config>
        <name>红颜一梦配置</name>
        <version>1.2</version>
        <settings>
            <option key="language" value="zh-CN"/>
            <option key="resolution" value="1920x1080"/>
            <option key="fullscreen" value="true"/>
        </settings>
    </config>
    <data>
        <item id="1" name="测试项目1" value="100"/>
        <item id="2" name="测试项目2" value="200"/>
    </data>
</root>"""
        
        # 创建测试XML文件
        xml_file = os.path.join(temp_dir, "test_single.xml")
        with open(xml_file, 'w', encoding='utf-8') as f:
            f.write(xml_content)
        
        print(f"\n✅ 创建测试XML文件: {xml_file}")
        print(f"原始内容长度: {len(xml_content)} 字符")
        
        # 4. 测试XML文件加密
        print("\n4. 测试XML文件加密...")
        encrypted_xml = crypto.encrypt_xml_file(xml_content)
        
        # 模拟单文件加密处理
        with open(xml_file, 'w', encoding='utf-8') as f:
            f.write(encrypted_xml)
        
        with open(xml_file, 'r', encoding='utf-8') as f:
            file_encrypted_xml = f.read()
        
        print(f"✅ XML加密成功，文件已覆盖")
        print(f"加密后内容长度: {len(file_encrypted_xml)} 字符")
        print(f"加密内容预览: {file_encrypted_xml[:80]}...")
        
        # 5. 测试XML文件解密
        print("\n5. 测试XML文件解密...")
        decrypted_xml = crypto.decrypt_xml_file(file_encrypted_xml)
        
        # 模拟单文件解密处理
        with open(xml_file, 'w', encoding='utf-8') as f:
            f.write(decrypted_xml)
        
        with open(xml_file, 'r', encoding='utf-8') as f:
            file_decrypted_xml = f.read()
        
        print(f"✅ XML解密成功，文件已覆盖")
        print(f"解密后内容长度: {len(file_decrypted_xml)} 字符")
        
        # 6. 验证XML内容一致性
        print("\n6. 验证XML文件内容一致性...")
        if file_decrypted_xml.strip() == xml_content.strip():
            print("✅ XML文件内容一致性验证通过！")
        else:
            print("❌ XML文件内容一致性验证失败！")
        
        print(f"\n📁 测试文件最终状态:")
        print(f"  Lua文件: {lua_file}")
        print(f"  XML文件: {xml_file}")
        print("🎉 v1.2版本单文件处理功能测试完成！")

def test_drag_drop_simulation():
    """模拟拖拽功能测试"""
    print("\n=== 模拟拖拽功能测试 ===")
    
    # 模拟拖拽路径处理
    test_paths = [
        "D:\\Game\\红颜一梦\\lua",
        "D:\\Game\\红颜一梦\\Scripts",
        "D:\\Game\\红颜一梦\\test.lua",  # 单个lua文件
        "D:\\Game\\红颜一梦\\config.xml",  # 单个xml文件
    ]
    
    for path in test_paths:
        print(f"\n模拟拖拽路径: {path}")
        
        if path.endswith('.lua'):
            print(f"  -> 检测到Lua文件，切换到单文件模式")
            print(f"  -> 自动识别为Lua文件类型")
        elif path.endswith('.xml'):
            print(f"  -> 检测到XML文件，切换到单文件模式")
            print(f"  -> 自动识别为XML文件类型")
        else:
            print(f"  -> 检测到目录，切换到批量处理模式")
            print(f"  -> 可选择处理的文件类型")
    
    print("✅ 拖拽路径处理逻辑测试完成！")

if __name__ == "__main__":
    test_single_file_processing()
    test_drag_drop_simulation()
    
    print("\n" + "="*60)
    print("🎉 v1.2版本所有新功能测试完成！")
    print("📋 新功能总结:")
    print("  ✅ 支持拖拽文件和文件夹到程序界面")
    print("  ✅ 支持单独处理lua和xml文件")
    print("  ✅ 支持批量处理文件夹")
    print("  ✅ 处理后直接覆盖原文件")
    print("  ✅ 智能模式切换和文件类型识别")
    print("  ✅ 优化的用户界面和操作体验")
    print("="*60)
