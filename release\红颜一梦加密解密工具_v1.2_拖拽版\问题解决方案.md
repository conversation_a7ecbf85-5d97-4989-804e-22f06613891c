# 红颜一梦加密解密工具 - 问题解决方案

## 🔧 v1.1版本更新说明

### 解决的问题
在v1.1版本中，我们解决了以下问题：

#### 1. tkinterdnd2依赖库兼容性问题
**问题描述**：
- 原计划使用`tkinterdnd2`库实现拖拽功能
- 该库在某些Windows环境下存在兼容性问题
- 可能导致程序无法启动，出现"Unable to load tkdnd library"错误

**解决方案**：
- 移除了`tkinterdnd2`依赖
- 改用更稳定的快捷键方式：Ctrl+V粘贴路径
- 保持了便捷操作的同时提高了兼容性

#### 2. 用户体验优化
**改进内容**：
- 简化了文件夹选择操作
- 添加了快捷键支持（Ctrl+V）
- 优化了界面提示信息
- 保持了直接覆盖原文件的功能

## 🚀 新的使用方式

### 快捷操作流程
1. **复制文件夹路径**：
   - 在文件管理器中右键文件夹
   - 选择"复制路径"或"复制为路径"
   
2. **粘贴到程序**：
   - 点击程序中的文件夹输入框
   - 按Ctrl+V粘贴路径
   
3. **开始处理**：
   - 选择操作类型（加密/解密）
   - 选择文件类型
   - 点击"开始处理"

### 传统操作方式
如果不习惯快捷键，仍可使用传统方式：
- 点击"浏览"按钮
- 在弹出的对话框中选择文件夹

## ⚠️ 重要提醒

### 文件备份
- **必须备份**：处理前务必备份重要文件
- **覆盖操作**：程序会直接覆盖原文件
- **不可恢复**：覆盖后无法自动恢复原文件

### 操作建议
1. **先测试**：使用示例文件测试功能
2. **小批量**：先处理少量文件验证效果
3. **分类处理**：分别处理lua和xml文件
4. **验证结果**：处理后检查文件是否正常

## 🔍 故障排除

### 程序无法启动
**可能原因**：
- 缺少必要的运行库
- 杀毒软件拦截
- 文件损坏

**解决方法**：
1. 确保Windows系统已更新
2. 将程序添加到杀毒软件白名单
3. 重新下载程序文件

### 处理失败
**可能原因**：
- 文件格式不正确
- 文件已损坏
- 权限不足

**解决方法**：
1. 检查文件是否为游戏原版格式
2. 确保程序有文件夹读写权限
3. 尝试以管理员身份运行

### 中文乱码
**可能原因**：
- 系统编码设置问题
- 文件编码不匹配

**解决方法**：
1. 确保系统支持UTF-8编码
2. 检查原文件编码格式
3. 使用支持UTF-8的文本编辑器查看

## 📋 技术细节

### 支持的文件格式
**Lua文件加密格式**：
- CRC格式：`数字@加密内容`
- ExtractString格式：`@特殊编码`

**XML文件加密格式**：
- CRC格式：`数字@加密内容`

### 加密算法
- **3DES加密**：使用TripleDES算法
- **Base64编码**：对加密数据编码
- **CRC16校验**：确保数据完整性
- **字符映射**：特定的字符转换

### 系统要求
- **操作系统**：Windows 7/8/10/11 (64位)
- **运行环境**：无需安装Python
- **磁盘空间**：至少50MB可用空间
- **内存要求**：至少512MB可用内存

## 🆘 获取帮助

### 常见问题
1. **Q**: 为什么有些文件解密失败？
   **A**: 可能文件不是游戏加密格式或已损坏

2. **Q**: 可以处理其他游戏的文件吗？
   **A**: 本工具专门为《红颜一梦》设计，不保证其他游戏兼容性

3. **Q**: 如何恢复被覆盖的文件？
   **A**: 只能从备份中恢复，建议处理前先备份

### 反馈渠道
如遇到问题或有改进建议，请通过相关渠道反馈：
- 详细描述问题现象
- 提供错误信息截图
- 说明操作系统版本
- 附上问题文件（如可能）

## 📈 版本历史

### v1.1 (当前版本)
- ✅ 修复了tkinterdnd2兼容性问题
- ✅ 新增Ctrl+V快捷键功能
- ✅ 优化了用户界面和提示
- ✅ 提高了程序稳定性

### v1.0 (初始版本)
- ✅ 基础加密解密功能
- ✅ 图形化操作界面
- ✅ 批量处理支持

---
**开发者**：基于红颜一梦游戏源码分析制作  
**版本**：v1.1  
**更新日期**：2024年8月19日
