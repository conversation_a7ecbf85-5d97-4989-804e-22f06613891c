# 红颜一梦加密解密工具 v1.2 新功能演示

## 🎯 v1.2版本重大更新

### ✨ 新增功能
1. **真正的拖拽支持** - 支持拖拽文件和文件夹到程序界面
2. **单文件处理模式** - 可以单独处理lua和xml文件
3. **智能模式切换** - 根据拖拽内容自动切换处理模式
4. **增强的用户体验** - 更直观的操作界面和反馈

## 🖱️ 拖拽功能演示

### 拖拽文件夹（批量处理模式）
```
操作步骤：
1. 从文件管理器中选择包含lua/xml文件的文件夹
2. 直接拖拽到程序的输入框中
3. 程序自动识别为文件夹，切换到"批量处理文件夹"模式
4. 可以选择处理的文件类型（lua/xml/全部）
5. 选择操作类型（加密/解密）
6. 点击"开始处理"

结果：
✅ 自动切换到批量处理模式
✅ 显示文件类型选择选项
✅ 处理文件夹中所有符合条件的文件
```

### 拖拽单个文件（单文件处理模式）
```
操作步骤：
1. 从文件管理器中选择单个lua或xml文件
2. 直接拖拽到程序的输入框中
3. 程序自动识别为文件，切换到"单独处理文件"模式
4. 自动识别文件类型（无需手动选择）
5. 选择操作类型（加密/解密）
6. 点击"开始处理"

结果：
✅ 自动切换到单文件处理模式
✅ 隐藏文件类型选择（自动识别）
✅ 只处理选中的单个文件
```

## 📁 处理模式对比

| 功能 | 批量处理文件夹 | 单独处理文件 |
|------|---------------|-------------|
| 输入类型 | 文件夹路径 | 单个文件路径 |
| 文件类型选择 | 手动选择 | 自动识别 |
| 处理范围 | 整个文件夹 | 单个文件 |
| 适用场景 | 批量修改 | 精确处理 |

## 🎮 使用场景示例

### 场景1：游戏MOD开发
```
需求：修改游戏中的AI.lua文件
操作：
1. 拖拽AI.lua文件到程序
2. 选择"解密文件"
3. 处理完成后用编辑器修改
4. 再次拖拽修改后的文件
5. 选择"加密文件"
6. 完成MOD制作
```

### 场景2：批量处理游戏资源
```
需求：解密整个lua文件夹查看游戏逻辑
操作：
1. 拖拽lua文件夹到程序
2. 选择"解密文件"
3. 选择"Lua文件(.lua)"
4. 批量解密所有lua文件
5. 用代码编辑器查看游戏逻辑
```

### 场景3：配置文件修改
```
需求：修改游戏配置XML文件
操作：
1. 拖拽config.xml文件到程序
2. 选择"解密文件"
3. 修改配置参数
4. 再次拖拽修改后的文件
5. 选择"加密文件"
6. 应用新配置
```

## 🔧 技术改进

### 拖拽实现
- 使用 `tkinterdnd2` 库实现真正的拖拽支持
- 自动处理文件路径解析和格式化
- 支持多种文件路径格式（包含空格、中文等）
- 容错处理，拖拽失败时提供友好提示

### 智能识别
- 自动检测拖拽内容是文件还是文件夹
- 根据文件扩展名自动识别文件类型
- 智能切换界面元素的显示/隐藏
- 提供实时的操作反馈

### 用户体验
- 直观的模式切换界面
- 清晰的操作提示和状态反馈
- 优化的界面布局和元素排列
- 增强的错误处理和用户提示

## ⚠️ 注意事项

### 拖拽功能要求
- 需要 `tkinterdnd2` 库支持
- 如果库不可用，程序会自动降级到传统操作方式
- 支持 Windows 7/8/10/11 系统

### 文件处理提醒
- **重要**：处理后会直接覆盖原文件
- **建议**：处理前务必备份重要文件
- **验证**：处理后检查文件是否正常

### 兼容性说明
- 单文件模式支持 .lua 和 .xml 文件
- 批量模式支持文件夹中的所有符合条件文件
- 自动跳过不支持的文件格式

## 🎉 升级建议

### 从v1.1升级到v1.2
1. **备份数据**：备份重要的游戏文件
2. **下载新版**：下载v1.2版本程序
3. **测试功能**：使用示例文件测试新功能
4. **开始使用**：享受更便捷的操作体验

### 新用户快速上手
1. **阅读说明**：查看使用说明.md文档
2. **测试示例**：使用示例文件夹中的文件测试
3. **小批量试用**：先处理少量文件验证效果
4. **正式使用**：处理实际的游戏文件

## 📞 反馈与支持

如果在使用v1.2版本时遇到问题：
1. 查看"问题解决方案.md"文档
2. 确认系统环境和权限设置
3. 尝试使用传统的浏览按钮方式
4. 通过相关渠道反馈问题

---
**开发者**：基于红颜一梦游戏源码分析制作  
**版本**：v1.2  
**更新日期**：2024年8月19日  
**主要改进**：真正的拖拽支持 + 单文件处理功能
