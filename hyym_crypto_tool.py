#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
红颜一梦 加密解密工具
基于游戏源码中的SaveManager类实现
支持lua文件和xml文件的加密解密
"""

import os
import sys
import base64
import hashlib
import random
import string
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from Crypto.Cipher import DES3
from Crypto.Util.Padding import pad, unpad
import threading

# Windows拖拽支持
try:
    import ctypes
    from ctypes import wintypes, windll, c_char_p, c_wchar_p, POINTER, byref
    from ctypes.wintypes import HWND, UINT, WPARAM, LPARAM, DWORD, POINT

    # Windows API 常量
    WM_DROPFILES = 0x0233
    MAX_PATH = 260

    # Windows API 函数定义
    DragAcceptFiles = windll.shell32.DragAcceptFiles
    DragAcceptFiles.argtypes = [HWND, wintypes.BOOL]

    DragQueryFile = windll.shell32.DragQueryFileW
    DragQueryFile.argtypes = [wintypes.HANDLE, UINT, c_wchar_p, UINT]
    DragQueryFile.restype = UINT

    DragFinish = windll.shell32.DragFinish
    DragFinish.argtypes = [wintypes.HANDLE]

    DND_AVAILABLE = True
except ImportError:
    DND_AVAILABLE = False


class HYYMCrypto:
    """红颜一梦加密解密类"""
    
    def __init__(self):
        # 密钥来自C#代码: "Yh$45Ct@mods"
        self.key_string = "Yh$45Ct@mods"
        self.key = hashlib.md5(self.key_string.encode('utf-8')).digest()
        
        # 字符映射表 (来自getResult方法)
        self.upper_chars = ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P',
                           'A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', 'Z',
                           'X', 'C', 'V', 'B', 'N', 'M']
        self.lower_chars = ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p',
                           'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'z',
                           'x', 'c', 'v', 'b', 'n', 'm']

    def _get_result(self, text):
        """字符大小写转换 (对应C#的getResult方法)"""
        result = []
        for char in text:
            if char in self.upper_chars:
                idx = self.upper_chars.index(char)
                result.append(self.lower_chars[idx])
            elif char in self.lower_chars:
                idx = self.lower_chars.index(char)
                result.append(self.upper_chars[idx])
            else:
                result.append(char)
        return ''.join(result)

    def _crc16_c(self, text):
        """CRC16校验 (对应C#的CRC16_C方法)"""
        data = text.encode('utf-8')
        crc_high = 0xFF
        crc_low = 0xFF
        
        for byte in data:
            crc_high ^= byte
            for _ in range(8):
                temp_high = crc_low
                temp_low = crc_high
                crc_low = crc_low >> 1
                crc_high = crc_high >> 1
                
                if temp_high & 1:
                    crc_high |= 0x80
                    
                if temp_low & 1:
                    crc_low ^= 0xA0
                    crc_high ^= 0x01
                    
        return f"{crc_low}{crc_high}"

    def _des3_encrypt(self, plaintext):
        """3DES加密 (对应C#的jm方法)"""
        cipher = DES3.new(self.key, DES3.MODE_ECB)
        padded_data = pad(plaintext.encode('utf-8'), DES3.block_size)
        encrypted = cipher.encrypt(padded_data)
        return base64.b64encode(encrypted).decode('utf-8')

    def _des3_decrypt(self, ciphertext):
        """3DES解密 (对应C#的m方法)"""
        try:
            cipher = DES3.new(self.key, DES3.MODE_ECB)
            encrypted_data = base64.b64decode(ciphertext)
            decrypted = cipher.decrypt(encrypted_data)
            return unpad(decrypted, DES3.block_size).decode('utf-8')
        except Exception:
            return ""

    def encrypt_save(self, source):
        """保存文件加密 (对应C#的Encrypt_Save方法)"""
        # Base64编码
        encoded = base64.b64encode(source.encode('utf-8')).decode('utf-8')
        
        # 处理末尾的=号
        if encoded.endswith('='):
            encoded = encoded[:-1] + '@'
            
        # 添加@前缀
        encoded = '@' + encoded
        
        # 随机插入#号
        random_count = random.randint(3, 8)
        for _ in range(random_count):
            pos = random.randint(1, len(encoded) - 2)
            encoded = encoded[:pos] + '#' + encoded[pos:]
            
        # 替换/为$
        encoded = encoded.replace('/', '$')
        
        # 字符转换
        return self._get_result(encoded)

    def decode_save(self, result):
        """保存文件解密 (对应C#的Decode_Save方法)"""
        try:
            # 字符转换
            result = self._get_result(result)
            
            # 处理末尾的@号
            if result.endswith('@'):
                result = result[:-1] + '='
                
            # 移除@前缀
            result = result[1:]
            
            # 移除#号和替换$为/
            result = result.replace('#', '').replace('$', '/')
            
            # Base64解码
            decoded = base64.b64decode(result).decode('utf-8')
            return decoded
        except Exception:
            return ""

    def extract_string(self, encrypted_str):
        """提取字符串 (对应C#的ExtractString方法)"""
        try:
            # 处理特殊字符替换和截取
            processed = encrypted_str.replace('\\', '0').replace('_', '1')
            processed = processed[1:-1]  # 移除首尾字符
            processed = processed.replace('/', '').replace('#', '/')
            
            # Base64解码
            decoded = base64.b64decode(processed).decode('utf-8')
            
            # 解压缩字符串
            result = []
            for i in range(0, len(decoded), 2):
                if i + 1 < len(decoded):
                    char = decoded[i]
                    count = int(decoded[i + 1])
                    result.append(char * count)
                    
            return ''.join(result)
        except Exception:
            return ""

    def crc_encrypt(self, plaintext):
        """CRC加密 (对应C#的crcjm方法)"""
        encrypted = self._des3_encrypt(plaintext)
        crc = self._crc16_c(plaintext)
        return f"{crc}@{encrypted}"

    def crc_decrypt(self, ciphertext):
        """CRC解密 (对应C#的crcm方法)"""
        try:
            parts = ciphertext.split('@')
            if len(parts) != 2:
                return ""
                
            crc_expected = parts[0]
            encrypted_data = parts[1]
            
            # 解密数据
            decrypted = self._des3_decrypt(encrypted_data)
            if not decrypted:
                return ""
                
            # 验证CRC
            crc_actual = self._crc16_c(decrypted)
            if crc_expected != crc_actual:
                return ""
                
            return decrypted
        except Exception:
            return ""

    def decrypt_lua_file(self, content):
        """解密Lua文件"""
        # 检查是否是CRC格式 (数字@内容)
        if '@' in content and not content.startswith('@'):
            return self.crc_decrypt(content)
        # 检查是否是ExtractString格式 (@开头)
        elif content.startswith('@'):
            return self.extract_string(content)
        return content

    def decrypt_xml_file(self, content):
        """解密XML文件"""
        if '@' in content:
            return self.crc_decrypt(content)
        return content

    def encrypt_lua_file(self, content):
        """加密Lua文件 (使用CRC加密方法)"""
        return self.crc_encrypt(content)

    def encrypt_xml_file(self, content):
        """加密XML文件"""
        return self.crc_encrypt(content)


class CryptoGUI:
    """图形界面类"""

    def __init__(self):
        self.crypto = HYYMCrypto()
        self.root = tk.Tk()
        self.hwnd = None
        self.original_wndproc = None
        self.setup_ui()
        self.setup_drag_drop()
        
    def setup_ui(self):
        """设置界面"""
        self.root.title("红颜一梦 加密解密工具 v1.2")
        self.root.geometry("680x620")
        self.root.resizable(True, True)

        # 设置窗口图标（如果存在）
        try:
            if os.path.exists("icon.ico"):
                self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="红颜一梦 加密解密工具 v1.2",
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        # 副标题
        subtitle_label = ttk.Label(main_frame, text="支持拖拽 • 单文件处理 • 批量处理 • 直接覆盖",
                                  font=("Microsoft YaHei", 10))
        subtitle_label.grid(row=1, column=0, columnspan=2, pady=(0, 20))
        
        # 处理模式选择
        ttk.Label(main_frame, text="处理模式:").grid(row=2, column=0, sticky=tk.W, pady=5)

        self.mode_var = tk.StringVar(value="folder")
        mode_frame = ttk.Frame(main_frame)
        mode_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Radiobutton(mode_frame, text="批量处理文件夹", variable=self.mode_var,
                       value="folder", command=self.on_mode_change).grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(mode_frame, text="单独处理文件", variable=self.mode_var,
                       value="file", command=self.on_mode_change).grid(row=0, column=1, sticky=tk.W)

        # 文件/文件夹选择
        self.path_label = ttk.Label(main_frame, text="选择文件夹:")
        self.path_label.grid(row=4, column=0, sticky=tk.W, pady=5)

        path_frame = ttk.Frame(main_frame)
        path_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        self.path_var = tk.StringVar()
        self.path_entry = ttk.Entry(path_frame, textvariable=self.path_var, width=50)
        self.path_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        self.browse_button = ttk.Button(path_frame, text="浏览文件夹",
                                       command=self.browse_path)
        self.browse_button.grid(row=0, column=1)

        path_frame.columnconfigure(0, weight=1)

        # 操作提示
        tip_text = "💡 提示：可以复制路径后按Ctrl+V粘贴，或点击浏览按钮选择"

        ttk.Label(main_frame, text=tip_text,
                 font=("Microsoft YaHei", 9), foreground="gray").grid(row=6, column=0, columnspan=2, sticky=tk.W)
        
        # 操作选择
        ttk.Label(main_frame, text="选择操作:").grid(row=7, column=0, sticky=tk.W, pady=(20, 5))

        self.operation_var = tk.StringVar(value="decrypt")
        ttk.Radiobutton(main_frame, text="解密文件", variable=self.operation_var,
                       value="decrypt").grid(row=8, column=0, sticky=tk.W)
        ttk.Radiobutton(main_frame, text="加密文件", variable=self.operation_var,
                       value="encrypt").grid(row=8, column=1, sticky=tk.W)

        # 文件类型选择（仅在文件夹模式下显示）
        self.file_type_label = ttk.Label(main_frame, text="文件类型:")
        self.file_type_label.grid(row=9, column=0, sticky=tk.W, pady=(20, 5))

        self.file_type_var = tk.StringVar(value="both")
        self.lua_radio = ttk.Radiobutton(main_frame, text="Lua文件(.lua)", variable=self.file_type_var,
                                        value="lua")
        self.lua_radio.grid(row=10, column=0, sticky=tk.W)
        self.xml_radio = ttk.Radiobutton(main_frame, text="XML文件(.xml)", variable=self.file_type_var,
                                        value="xml")
        self.xml_radio.grid(row=10, column=1, sticky=tk.W)
        self.both_radio = ttk.Radiobutton(main_frame, text="全部文件", variable=self.file_type_var,
                                         value="both")
        self.both_radio.grid(row=11, column=0, sticky=tk.W)

        # 覆盖模式说明
        ttk.Label(main_frame, text="⚠️ 注意：处理后将直接覆盖原文件，请提前备份！",
                 font=("Microsoft YaHei", 9), foreground="red").grid(row=12, column=0, columnspan=2, sticky=tk.W, pady=(10, 0))
        
        # 执行按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=13, column=0, columnspan=2, pady=20)

        ttk.Button(button_frame, text="开始处理",
                  command=self.start_processing).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="清空日志",
                  command=self.clear_log).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关于",
                  command=self.show_about).pack(side=tk.LEFT)

        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=14, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 日志区域
        ttk.Label(main_frame, text="处理日志:").grid(row=15, column=0, sticky=tk.W, pady=(20, 5))

        log_frame = ttk.Frame(main_frame)
        log_frame.grid(row=16, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        self.log_text = tk.Text(log_frame, height=10, width=70)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 配置权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(16, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # 绑定快捷键
        self.root.bind('<Control-v>', self.handle_paste_path)
        self.path_entry.bind('<Control-v>', self.handle_paste_path)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def on_mode_change(self):
        """处理模式切换"""
        mode = self.mode_var.get()
        if mode == "folder":
            self.path_label.config(text="选择文件夹:")
            self.browse_button.config(text="浏览文件夹")
            # 显示文件类型选择
            self.file_type_label.grid()
            self.lua_radio.grid()
            self.xml_radio.grid()
            self.both_radio.grid()
        else:  # file mode
            self.path_label.config(text="选择文件:")
            self.browse_button.config(text="浏览文件")
            # 隐藏文件类型选择（单文件模式下自动识别）
            self.file_type_label.grid_remove()
            self.lua_radio.grid_remove()
            self.xml_radio.grid_remove()
            self.both_radio.grid_remove()

    def setup_drag_drop(self):
        """设置拖拽支持"""
        if not DND_AVAILABLE:
            self.log_message("⚠️ 拖拽功能不可用：Windows API加载失败")
            self.log_message("💡 请使用浏览按钮或Ctrl+V粘贴路径")
            return

        try:
            # 等待窗口完全创建
            self.root.update()

            # 获取窗口句柄
            self.hwnd = self.root.winfo_id()
            if not self.hwnd:
                self.log_message("⚠️ 无法获取窗口句柄，拖拽功能不可用")
                return

            # 启用拖拽接受
            DragAcceptFiles(self.hwnd, True)

            # 设置窗口过程钩子
            self.setup_window_proc()

            self.log_message("✅ 拖拽功能已启用")
            self.log_message("💡 可以拖拽文件或文件夹到程序窗口")

        except Exception as e:
            self.log_message(f"⚠️ 拖拽功能初始化失败: {str(e)}")
            self.log_message("💡 请使用浏览按钮或Ctrl+V粘贴路径")

    def setup_window_proc(self):
        """设置窗口过程钩子"""
        try:
            # 导入必要的Windows API
            from ctypes import WINFUNCTYPE

            # 定义窗口过程函数类型
            WNDPROC = WINFUNCTYPE(ctypes.c_long, HWND, UINT, WPARAM, LPARAM)

            # 获取原始窗口过程
            GetWindowLongPtr = windll.user32.GetWindowLongPtrW
            GetWindowLongPtr.argtypes = [HWND, ctypes.c_int]
            GetWindowLongPtr.restype = ctypes.c_void_p

            SetWindowLongPtr = windll.user32.SetWindowLongPtrW
            SetWindowLongPtr.argtypes = [HWND, ctypes.c_int, ctypes.c_void_p]
            SetWindowLongPtr.restype = ctypes.c_void_p

            CallWindowProc = windll.user32.CallWindowProcW
            CallWindowProc.argtypes = [ctypes.c_void_p, HWND, UINT, WPARAM, LPARAM]
            CallWindowProc.restype = ctypes.c_long

            # 保存原始窗口过程
            GWL_WNDPROC = -4
            self.original_wndproc = GetWindowLongPtr(self.hwnd, GWL_WNDPROC)

            # 创建新的窗口过程
            def new_wndproc(hwnd, msg, wparam, lparam):
                if msg == WM_DROPFILES:
                    self.handle_drop_files(wparam)
                    return 0
                else:
                    return CallWindowProc(self.original_wndproc, hwnd, msg, wparam, lparam)

            # 设置新的窗口过程
            self.new_wndproc = WNDPROC(new_wndproc)
            SetWindowLongPtr(self.hwnd, GWL_WNDPROC, ctypes.cast(self.new_wndproc, ctypes.c_void_p).value)

        except Exception as e:
            self.log_message(f"⚠️ 窗口过程钩子设置失败: {str(e)}")

    def handle_drop_files(self, hdrop):
        """处理拖拽文件"""
        try:
            # 获取拖拽文件数量
            file_count = DragQueryFile(hdrop, 0xFFFFFFFF, None, 0)

            if file_count == 0:
                self.root.after(0, lambda: self.log_message("⚠️ 未检测到拖拽文件"))
                return

            # 获取第一个文件路径（只处理第一个）
            buffer_size = MAX_PATH * 2  # 增加缓冲区大小以支持长路径
            buffer = ctypes.create_unicode_buffer(buffer_size)

            if DragQueryFile(hdrop, 0, buffer, buffer_size) > 0:
                file_path = buffer.value

                # 验证路径
                if not file_path or len(file_path.strip()) == 0:
                    self.root.after(0, lambda: self.log_message("❌ 获取到空的文件路径"))
                    return

                # 在主线程中处理拖拽，使用lambda避免参数传递问题
                self.root.after(0, lambda path=file_path: self.process_dropped_path(path))

                # 如果有多个文件，提示用户
                if file_count > 1:
                    self.root.after(0, lambda: self.log_message(
                        f"💡 检测到{file_count}个项目，已处理第一个: {os.path.basename(file_path)}"
                    ))
            else:
                self.root.after(0, lambda: self.log_message("❌ 无法获取拖拽文件路径"))

        except Exception as e:
            # 确保错误信息在主线程中显示
            error_msg = f"❌ 处理拖拽文件时出错: {str(e)}"
            try:
                self.root.after(0, lambda: self.log_message(error_msg))
            except:
                print(error_msg)  # 如果UI也出问题，至少在控制台显示
        finally:
            # 确保总是完成拖拽操作
            try:
                DragFinish(hdrop)
            except:
                pass

    def process_dropped_path(self, path):
        """处理拖拽的路径"""
        try:
            # 验证路径
            if not path or len(path.strip()) == 0:
                self.log_message("❌ 拖拽路径为空")
                return

            path = path.strip()  # 清理路径

            if not os.path.exists(path):
                self.log_message(f"❌ 拖拽路径不存在: {path}")
                return

            # 检查路径长度（Windows路径限制）
            if len(path) > 260:
                self.log_message(f"⚠️ 路径过长，可能导致处理问题: {len(path)} 字符")

            # 设置路径
            self.path_var.set(path)
            self.log_message(f"📍 拖拽路径: {path}")

            if os.path.isfile(path):
                # 拖拽的是文件
                self._handle_dropped_file(path)

            elif os.path.isdir(path):
                # 拖拽的是文件夹
                self._handle_dropped_folder(path)
            else:
                self.log_message(f"❌ 无法识别的路径类型: {path}")

        except PermissionError:
            self.log_message(f"❌ 没有访问权限: {path}")
            self.log_message(f"💡 请尝试以管理员身份运行程序")
        except Exception as e:
            self.log_message(f"❌ 处理拖拽路径时出错: {str(e)}")
            self.log_message(f"💡 请检查文件路径是否正确，或尝试使用浏览按钮")

    def _handle_dropped_file(self, path):
        """处理拖拽的文件"""
        try:
            file_ext = os.path.splitext(path)[1].lower()
            file_name = os.path.basename(path)

            if file_ext in ['.lua', '.xml']:
                # 切换到文件模式
                self.mode_var.set("file")
                self.on_mode_change()

                file_type_name = "Lua" if file_ext == '.lua' else "XML"
                self.log_message(f"📄 已拖拽{file_type_name}文件: {file_name}")
                self.log_message(f"✅ 自动切换到单文件处理模式")

                # 检查文件大小
                try:
                    file_size = os.path.getsize(path)
                    if file_size > 10 * 1024 * 1024:  # 10MB
                        self.log_message(f"⚠️ 文件较大 ({file_size / 1024 / 1024:.1f}MB)，处理可能需要时间")
                    elif file_size == 0:
                        self.log_message(f"⚠️ 文件为空")
                except:
                    pass

            else:
                self.log_message(f"⚠️ 不支持的文件类型: {file_ext}")
                self.log_message(f"📄 已拖拽文件: {file_name}")
                self.log_message(f"💡 支持的文件类型: .lua, .xml")

        except Exception as e:
            self.log_message(f"❌ 处理文件时出错: {str(e)}")

    def _handle_dropped_folder(self, path):
        """处理拖拽的文件夹"""
        try:
            folder_name = os.path.basename(path)

            # 切换到文件夹模式
            self.mode_var.set("folder")
            self.on_mode_change()

            self.log_message(f"📁 已拖拽文件夹: {folder_name}")
            self.log_message(f"✅ 自动切换到批量处理模式")

            # 快速检查文件夹是否为空
            try:
                if not any(os.scandir(path)):
                    self.log_message(f"⚠️ 文件夹为空")
                    return
            except PermissionError:
                self.log_message(f"❌ 无法访问文件夹，权限不足")
                return
            except Exception:
                pass  # 继续处理

            # 异步检查文件夹中的文件类型（避免卡死）
            self.log_message(f"🔍 正在分析文件夹内容...")

            # 在后台线程中分析文件夹
            import threading
            analysis_thread = threading.Thread(
                target=self._analyze_folder_async,
                args=(path,),
                daemon=True
            )
            analysis_thread.start()

        except Exception as e:
            self.log_message(f"❌ 处理文件夹时出错: {str(e)}")

    def _analyze_folder_async(self, path):
        """异步分析文件夹内容（避免UI卡死）"""
        try:
            lua_count = 0
            xml_count = 0
            total_files = 0
            max_files_to_scan = 1000  # 限制扫描文件数量

            # 使用生成器避免一次性加载所有文件
            for root, dirs, files in os.walk(path):
                # 限制扫描深度，避免过深的目录结构
                level = root.replace(path, '').count(os.sep)
                if level >= 3:  # 最多扫描3层深度
                    dirs[:] = []  # 清空dirs列表，停止深入
                    continue

                for file in files:
                    total_files += 1
                    if total_files > max_files_to_scan:
                        # 如果文件太多，停止扫描
                        self.root.after(0, lambda: self.log_message(
                            f"⚠️ 文件夹包含超过{max_files_to_scan}个文件，已停止完整扫描"
                        ))
                        break

                    file_ext = os.path.splitext(file)[1].lower()
                    if file_ext == '.lua':
                        lua_count += 1
                    elif file_ext == '.xml':
                        xml_count += 1

                if total_files > max_files_to_scan:
                    break

            # 在主线程中更新UI
            self.root.after(0, lambda: self._update_folder_analysis_result(
                lua_count, xml_count, total_files, max_files_to_scan
            ))

        except Exception as e:
            # 在主线程中显示错误
            self.root.after(0, lambda: self.log_message(
                f"❌ 分析文件夹时出错: {str(e)}"
            ))

    def _update_folder_analysis_result(self, lua_count, xml_count, total_files, max_files_to_scan):
        """更新文件夹分析结果（在主线程中调用）"""
        try:
            if total_files > max_files_to_scan:
                self.log_message(f"📊 已扫描 {max_files_to_scan}+ 个文件（部分扫描）")
            else:
                self.log_message(f"📊 已扫描 {total_files} 个文件")

            if lua_count > 0 and xml_count > 0:
                self.file_type_var.set("both")
                self.log_message(f"💡 发现 {lua_count} 个Lua文件和 {xml_count} 个XML文件")
                self.log_message(f"✅ 已自动选择'全部文件'类型")
            elif lua_count > 0:
                self.file_type_var.set("lua")
                self.log_message(f"💡 发现 {lua_count} 个Lua文件")
                self.log_message(f"✅ 已自动选择'Lua文件(.lua)'类型")
            elif xml_count > 0:
                self.file_type_var.set("xml")
                self.log_message(f"💡 发现 {xml_count} 个XML文件")
                self.log_message(f"✅ 已自动选择'XML文件(.xml)'类型")
            else:
                self.log_message(f"⚠️ 未发现支持的文件类型(.lua/.xml)")
                self.log_message(f"💡 您可以手动选择文件类型进行处理")

        except Exception as e:
            self.log_message(f"❌ 更新分析结果时出错: {str(e)}")



    def handle_paste_path(self, event=None):
        """处理粘贴路径（Ctrl+V）"""
        try:
            # 从剪贴板获取路径
            clipboard_text = self.root.clipboard_get()
            if clipboard_text and os.path.exists(clipboard_text):
                self.path_var.set(clipboard_text)
                if os.path.isdir(clipboard_text):
                    self.log_message(f"📁 已粘贴文件夹路径: {clipboard_text}")
                    # 自动切换到文件夹模式
                    self.mode_var.set("folder")
                    self.on_mode_change()
                elif os.path.isfile(clipboard_text):
                    self.log_message(f"📄 已粘贴文件路径: {clipboard_text}")
                    # 自动切换到文件模式
                    self.mode_var.set("file")
                    self.on_mode_change()
        except:
            pass

    def show_about(self):
        """显示关于对话框"""
        about_text = """红颜一梦 加密解密工具 v1.2 稳定版

基于游戏源码中的SaveManager类实现
支持lua文件和xml文件的加密解密

功能特点：
• 支持批量处理文件夹
• 支持单独处理文件
• 支持快捷键粘贴路径（Ctrl+V）
• 自动识别加密格式
• 支持CRC校验
• 图形化操作界面
• 直接覆盖原文件

使用说明：
1. 选择处理模式（批量处理文件夹/单独处理文件）
2. 浏览选择或复制路径后按Ctrl+V粘贴
3. 选择加密或解密操作
4. 点击开始处理

⚠️ 重要：处理后会直接覆盖原文件，请提前备份！

开发者：基于红颜一梦游戏源码分析制作
版本：v1.2 稳定版"""

        messagebox.showinfo("关于", about_text)

    def browse_path(self):
        """浏览文件或文件夹"""
        mode = self.mode_var.get()
        if mode == "folder":
            path = filedialog.askdirectory()
            if path:
                self.path_var.set(path)
                self.log_message(f"📁 已选择文件夹: {path}")
        else:  # file mode
            path = filedialog.askopenfilename(
                title="选择要处理的文件",
                filetypes=[
                    ("Lua文件", "*.lua"),
                    ("XML文件", "*.xml"),
                    ("所有文件", "*.*")
                ]
            )
            if path:
                self.path_var.set(path)
                self.log_message(f"📄 已选择文件: {path}")

    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update()

    def start_processing(self):
        """开始处理"""
        path = self.path_var.get().strip()
        if not path:
            messagebox.showerror("错误", "请选择文件或文件夹")
            return

        if not os.path.exists(path):
            messagebox.showerror("错误", "路径不存在")
            return

        mode = self.mode_var.get()

        # 在新线程中执行处理
        if mode == "folder":
            thread = threading.Thread(target=self.process_folder, args=(path,))
        else:
            thread = threading.Thread(target=self.process_single_file, args=(path,))

        thread.daemon = True
        thread.start()

    def process_single_file(self, file_path):
        """处理单个文件"""
        try:
            self.progress.start()
            self.log_message(f"\n🔄 开始处理文件: {os.path.basename(file_path)}")

            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            operation = self.operation_var.get()
            file_ext = os.path.splitext(file_path)[1].lower()

            if operation == "decrypt":
                # 解密文件
                if file_ext == '.lua':
                    result = self.crypto.decrypt_lua_file(content)
                elif file_ext == '.xml':
                    result = self.crypto.decrypt_xml_file(content)
                else:
                    self.log_message(f"❌ 不支持的文件类型: {file_ext}")
                    return

                if result and result != content:
                    # 直接覆盖原文件
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(result)
                    self.log_message(f"✅ 解密成功: {os.path.basename(file_path)} (已覆盖原文件)")
                elif result == content:
                    self.log_message(f"⚠️ 跳过: {os.path.basename(file_path)} (文件可能已经是明文)")
                else:
                    self.log_message(f"❌ 解密失败: {os.path.basename(file_path)} (格式不正确或已损坏)")

            else:  # encrypt
                # 加密文件
                if file_ext == '.lua':
                    result = self.crypto.encrypt_lua_file(content)
                elif file_ext == '.xml':
                    result = self.crypto.encrypt_xml_file(content)
                else:
                    self.log_message(f"❌ 不支持的文件类型: {file_ext}")
                    return

                if result:
                    # 直接覆盖原文件
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(result)
                    self.log_message(f"✅ 加密成功: {os.path.basename(file_path)} (已覆盖原文件)")
                else:
                    self.log_message(f"❌ 加密失败: {os.path.basename(file_path)}")

            self.log_message(f"\n🎉 文件处理完成!")
            self.log_message("📁 文件已直接覆盖更新")
            self.log_message("⚠️ 如需恢复原文件，请使用备份文件")

        except Exception as e:
            self.log_message(f"❌ 处理文件时出错: {str(e)}")
        finally:
            self.progress.stop()

    def process_folder(self, folder_path):
        """处理文件夹"""
        try:
            self.progress.start()
            self.log_text.delete(1.0, tk.END)

            operation = self.operation_var.get()
            file_type = self.file_type_var.get()

            self.log_message(f"开始{('解密' if operation == 'decrypt' else '加密')}处理...")
            self.log_message(f"目标文件夹: {folder_path}")
            
            processed_count = 0
            
            # 处理文件
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_ext = os.path.splitext(file)[1].lower()
                    
                    should_process = False
                    if file_type == "both":
                        should_process = file_ext in ['.lua', '.xml']
                    elif file_type == "lua":
                        should_process = file_ext == '.lua'
                    elif file_type == "xml":
                        should_process = file_ext == '.xml'
                    
                    if should_process:
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            
                            if operation == "decrypt":
                                if file_ext == '.lua':
                                    result = self.crypto.decrypt_lua_file(content)
                                else:  # .xml
                                    result = self.crypto.decrypt_xml_file(content)
                                    
                                if result and result != content:
                                    # 直接覆盖原文件
                                    with open(file_path, 'w', encoding='utf-8') as f:
                                        f.write(result)
                                    self.log_message(f"✅ 解密成功: {file} (已覆盖原文件)")
                                    processed_count += 1
                                elif result == content:
                                    self.log_message(f"⚠️ 跳过: {file} (文件可能已经是明文)")
                                else:
                                    self.log_message(f"❌ 解密失败: {file} (格式不正确或已损坏)")
                            else:  # encrypt
                                if file_ext == '.lua':
                                    result = self.crypto.encrypt_lua_file(content)
                                else:  # .xml
                                    result = self.crypto.encrypt_xml_file(content)
                                    
                                # 直接覆盖原文件
                                with open(file_path, 'w', encoding='utf-8') as f:
                                    f.write(result)
                                self.log_message(f"✅ 加密成功: {file} (已覆盖原文件)")
                                processed_count += 1
                                
                        except Exception as e:
                            self.log_message(f"❌ 处理文件 {file} 时出错: {str(e)}")

            self.log_message(f"\n🎉 处理完成! 共处理 {processed_count} 个文件")
            if processed_count > 0:
                self.log_message("📁 所有文件已直接覆盖更新")
                self.log_message("⚠️ 如需恢复原文件，请使用备份文件")
            else:
                self.log_message("⚠️ 没有找到需要处理的文件，请检查文件夹路径和文件类型")
            
        except Exception as e:
            self.log_message(f"处理过程中出错: {str(e)}")
        finally:
            self.progress.stop()

    def cleanup(self):
        """清理资源"""
        try:
            # 恢复原始窗口过程
            if hasattr(self, 'hwnd') and hasattr(self, 'original_wndproc') and self.hwnd and self.original_wndproc:
                from ctypes import windll
                SetWindowLongPtr = windll.user32.SetWindowLongPtrW
                GWL_WNDPROC = -4
                SetWindowLongPtr(self.hwnd, GWL_WNDPROC, self.original_wndproc)

            # 禁用拖拽接受
            if hasattr(self, 'hwnd') and self.hwnd and DND_AVAILABLE:
                DragAcceptFiles(self.hwnd, False)

        except Exception:
            # 忽略清理时的错误
            pass

    def on_closing(self):
        """窗口关闭时的处理"""
        self.cleanup()
        self.root.destroy()

    def run(self):
        """运行界面"""
        # 设置关闭事件处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


if __name__ == "__main__":
    app = CryptoGUI()
    app.run()
