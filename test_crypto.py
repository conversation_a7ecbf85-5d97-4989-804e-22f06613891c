#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试加密解密功能
"""

import os
import sys
sys.path.append('.')

from hyym_crypto_tool import HYYMCrypto

def test_with_sample_files():
    """使用示例文件测试"""
    crypto = HYYMCrypto()
    
    print("=== 红颜一梦加密解密工具测试 ===\n")
    
    # 测试路径 - 使用脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    lua_encrypted_path = os.path.join(script_dir, "gamedata", "suyu", "HYYM", "lua", "AI.lua")
    lua_decrypted_path = os.path.join(script_dir, "gamedata", "suyu", "HYYM", "lua", "AI_decrypted.lua")
    xml_encrypted_path = os.path.join(script_dir, "gamedata", "suyu", "HYYM", "Scripts", "touch.xml")
    xml_decrypted_path = os.path.join(script_dir, "gamedata", "suyu", "HYYM", "Scripts", "touch_decrypted.xml")

    print(f"脚本目录: {script_dir}")
    print(f"查找Lua文件: {lua_encrypted_path}")
    print(f"Lua文件存在: {os.path.exists(lua_encrypted_path)}")
    print(f"查找XML文件: {xml_encrypted_path}")
    print(f"XML文件存在: {os.path.exists(xml_encrypted_path)}")
    print()
    
    # 测试Lua文件解密
    print("1. 测试Lua文件解密...")
    if os.path.exists(lua_encrypted_path) and os.path.exists(lua_decrypted_path):
        try:
            # 读取加密文件
            with open(lua_encrypted_path, 'r', encoding='utf-8') as f:
                encrypted_content = f.read()
            
            # 读取已知的解密文件作为参考
            with open(lua_decrypted_path, 'r', encoding='utf-8') as f:
                expected_content = f.read()
            
            # 解密
            decrypted_content = crypto.decrypt_lua_file(encrypted_content)
            
            print(f"   加密文件长度: {len(encrypted_content)}")
            print(f"   解密结果长度: {len(decrypted_content)}")
            print(f"   期望结果长度: {len(expected_content)}")
            
            # 比较前100个字符
            if decrypted_content:
                print(f"   解密结果预览: {decrypted_content[:100]}...")
                print(f"   期望结果预览: {expected_content[:100]}...")
                
                if decrypted_content.strip() == expected_content.strip():
                    print("   ✅ Lua解密测试通过!")
                else:
                    print("   ❌ Lua解密结果与期望不符")
            else:
                print("   ❌ Lua解密失败")
                
        except Exception as e:
            print(f"   ❌ Lua测试出错: {e}")
    else:
        print("   ⚠️ 找不到Lua测试文件")
    
    print()
    
    # 测试XML文件解密
    print("2. 测试XML文件解密...")
    if os.path.exists(xml_encrypted_path) and os.path.exists(xml_decrypted_path):
        try:
            # 读取加密文件
            with open(xml_encrypted_path, 'r', encoding='utf-8') as f:
                encrypted_content = f.read()
            
            # 读取已知的解密文件作为参考
            with open(xml_decrypted_path, 'r', encoding='utf-8') as f:
                expected_content = f.read()
            
            # 解密
            decrypted_content = crypto.decrypt_xml_file(encrypted_content)
            
            print(f"   加密文件长度: {len(encrypted_content)}")
            print(f"   解密结果长度: {len(decrypted_content)}")
            print(f"   期望结果长度: {len(expected_content)}")
            
            # 比较前200个字符
            if decrypted_content:
                print(f"   解密结果预览: {decrypted_content[:200]}...")
                print(f"   期望结果预览: {expected_content[:200]}...")
                
                if decrypted_content.strip() == expected_content.strip():
                    print("   ✅ XML解密测试通过!")
                else:
                    print("   ❌ XML解密结果与期望不符")
            else:
                print("   ❌ XML解密失败")
                
        except Exception as e:
            print(f"   ❌ XML测试出错: {e}")
    else:
        print("   ⚠️ 找不到XML测试文件")
    
    print()
    
    # 测试加密功能
    print("3. 测试加密功能...")
    test_text = "这是一个测试文本\nfunction test() return 'hello' end"
    
    try:
        # 测试Lua加密
        lua_encrypted = crypto.encrypt_lua_file(test_text)
        lua_decrypted = crypto.decrypt_lua_file(lua_encrypted)
        
        print(f"   原文: {test_text}")
        print(f"   Lua加密结果: {lua_encrypted[:100]}...")
        print(f"   Lua解密结果: {lua_decrypted}")
        
        if lua_decrypted == test_text:
            print("   ✅ Lua加密解密循环测试通过!")
        else:
            print("   ❌ Lua加密解密循环测试失败")
        
        # 测试XML加密
        xml_encrypted = crypto.encrypt_xml_file(test_text)
        xml_decrypted = crypto.decrypt_xml_file(xml_encrypted)
        
        print(f"   XML加密结果: {xml_encrypted[:100]}...")
        print(f"   XML解密结果: {xml_decrypted}")
        
        if xml_decrypted == test_text:
            print("   ✅ XML加密解密循环测试通过!")
        else:
            print("   ❌ XML加密解密循环测试失败")
            
    except Exception as e:
        print(f"   ❌ 加密测试出错: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_with_sample_files()
