#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试拖拽闪退问题
"""

import os
import sys
import traceback
import tempfile
import logging
from datetime import datetime

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('drag_debug.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def create_simple_test_files():
    """创建简单的测试文件"""
    try:
        logging.info("创建简单测试文件...")
        
        temp_dir = tempfile.mkdtemp(prefix="drag_debug_")
        logging.info(f"测试目录: {temp_dir}")
        
        # 创建一个简单的lua文件
        lua_file = os.path.join(temp_dir, "simple_test.lua")
        with open(lua_file, 'w', encoding='utf-8') as f:
            f.write("-- 简单测试文件\nprint('Hello World')")
        
        # 创建一个简单的xml文件
        xml_file = os.path.join(temp_dir, "simple_test.xml")
        with open(xml_file, 'w', encoding='utf-8') as f:
            f.write("<?xml version='1.0'?><test>简单测试</test>")
        
        # 创建一个小的子文件夹
        sub_dir = os.path.join(temp_dir, "subfolder")
        os.makedirs(sub_dir)
        
        sub_lua = os.path.join(sub_dir, "sub_test.lua")
        with open(sub_lua, 'w', encoding='utf-8') as f:
            f.write("-- 子文件夹测试\nprint('Sub test')")
        
        logging.info(f"创建了测试文件: {lua_file}, {xml_file}, {sub_lua}")
        return temp_dir, lua_file, xml_file, sub_dir
        
    except Exception as e:
        logging.error(f"创建测试文件失败: {e}")
        logging.error(traceback.format_exc())
        return None, None, None, None

def test_drag_functions():
    """测试拖拽相关函数"""
    try:
        logging.info("开始测试拖拽相关函数...")
        
        # 测试导入
        try:
            from hyym_crypto_tool import DND_AVAILABLE, CryptoGUI
            logging.info(f"DND_AVAILABLE: {DND_AVAILABLE}")
        except Exception as e:
            logging.error(f"导入失败: {e}")
            return False
        
        # 测试Windows API
        if DND_AVAILABLE:
            try:
                import ctypes
                from ctypes import windll
                
                # 测试基本API调用
                user32 = windll.user32
                logging.info("Windows API 可用")
                
                # 测试获取桌面窗口句柄
                desktop_hwnd = user32.GetDesktopWindow()
                logging.info(f"桌面窗口句柄: {desktop_hwnd}")
                
            except Exception as e:
                logging.error(f"Windows API 测试失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        logging.error(f"测试拖拽函数失败: {e}")
        logging.error(traceback.format_exc())
        return False

def test_gui_creation():
    """测试GUI创建"""
    try:
        logging.info("测试GUI创建...")
        
        import tkinter as tk
        from hyym_crypto_tool import CryptoGUI
        
        # 创建根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        logging.info("Tkinter根窗口创建成功")
        
        # 尝试创建GUI实例
        try:
            gui = CryptoGUI(root)
            logging.info("CryptoGUI实例创建成功")
            
            # 测试窗口句柄获取
            try:
                hwnd = root.winfo_id()
                logging.info(f"窗口句柄: {hwnd}")
            except Exception as e:
                logging.error(f"获取窗口句柄失败: {e}")
            
            root.destroy()
            return True
            
        except Exception as e:
            logging.error(f"CryptoGUI创建失败: {e}")
            logging.error(traceback.format_exc())
            root.destroy()
            return False
            
    except Exception as e:
        logging.error(f"GUI测试失败: {e}")
        logging.error(traceback.format_exc())
        return False

def run_minimal_test():
    """运行最小化测试"""
    try:
        logging.info("运行最小化拖拽测试...")
        
        import tkinter as tk
        
        # 创建最简单的窗口
        root = tk.Tk()
        root.title("拖拽测试 - 最小版本")
        root.geometry("400x300")
        
        # 添加标签
        label = tk.Label(root, text="这是最小化测试窗口\n如果这个窗口正常显示，说明基础功能OK", 
                        font=("Arial", 12), pady=20)
        label.pack()
        
        # 添加测试按钮
        def test_button_click():
            try:
                logging.info("测试按钮被点击")
                label.config(text="按钮点击测试成功！\n基础GUI功能正常")
            except Exception as e:
                logging.error(f"按钮点击测试失败: {e}")
        
        test_btn = tk.Button(root, text="点击测试", command=test_button_click)
        test_btn.pack(pady=10)
        
        # 添加退出按钮
        exit_btn = tk.Button(root, text="退出", command=root.quit)
        exit_btn.pack(pady=5)
        
        logging.info("最小化测试窗口创建成功，开始运行...")
        
        # 设置5秒后自动关闭
        root.after(5000, root.quit)
        
        root.mainloop()
        root.destroy()
        
        logging.info("最小化测试完成")
        return True
        
    except Exception as e:
        logging.error(f"最小化测试失败: {e}")
        logging.error(traceback.format_exc())
        return False

def diagnose_crash():
    """诊断崩溃原因"""
    print("🔍 开始诊断拖拽闪退问题...")
    print("="*60)
    
    # 检查Python环境
    print(f"Python版本: {sys.version}")
    print(f"平台: {sys.platform}")
    print(f"架构: {sys.maxsize > 2**32 and '64位' or '32位'}")
    
    # 检查必要模块
    modules_to_check = ['tkinter', 'ctypes', 'threading', 'os', 'tempfile']
    for module in modules_to_check:
        try:
            __import__(module)
            print(f"✅ {module} 模块可用")
        except ImportError as e:
            print(f"❌ {module} 模块不可用: {e}")
    
    print("\n" + "="*60)
    
    # 测试拖拽函数
    if test_drag_functions():
        print("✅ 拖拽函数测试通过")
    else:
        print("❌ 拖拽函数测试失败")
    
    # 测试GUI创建
    if test_gui_creation():
        print("✅ GUI创建测试通过")
    else:
        print("❌ GUI创建测试失败")
    
    # 创建测试文件
    temp_dir, lua_file, xml_file, sub_dir = create_simple_test_files()
    if temp_dir:
        print(f"✅ 测试文件创建成功: {temp_dir}")
    else:
        print("❌ 测试文件创建失败")
    
    print("\n" + "="*60)
    print("🧪 运行最小化测试...")
    
    if run_minimal_test():
        print("✅ 最小化测试通过")
    else:
        print("❌ 最小化测试失败")
    
    print("\n" + "="*60)
    print("📋 诊断总结:")
    print("1. 检查 drag_debug.log 文件查看详细日志")
    print("2. 如果最小化测试失败，问题可能在基础GUI")
    print("3. 如果最小化测试成功，问题可能在拖拽功能")
    print("4. 请提供 drag_debug.log 文件内容以进一步分析")
    
    if temp_dir:
        print(f"\n📁 测试文件位置: {temp_dir}")
        print("可以尝试手动拖拽这些文件到程序测试")

def main():
    """主函数"""
    try:
        print("🔧 红颜一梦加密解密工具 - 拖拽闪退问题诊断")
        print("="*60)
        
        # 记录开始时间
        start_time = datetime.now()
        logging.info(f"诊断开始: {start_time}")
        
        # 运行诊断
        diagnose_crash()
        
        # 记录结束时间
        end_time = datetime.now()
        logging.info(f"诊断结束: {end_time}")
        logging.info(f"总耗时: {end_time - start_time}")
        
        print(f"\n🎯 诊断完成！请查看 drag_debug.log 文件获取详细信息")
        
    except Exception as e:
        print(f"❌ 诊断过程出错: {e}")
        logging.error(f"诊断过程出错: {e}")
        logging.error(traceback.format_exc())

if __name__ == "__main__":
    main()
